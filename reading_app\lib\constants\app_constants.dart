import 'package:flutter/material.dart';

/// 应用常量定义
class AppConstants {
  // 颜色常量
  static const Color primaryColor = Color(0xFFFF6B35);
  static const Color secondaryColor = Color(0xFF2C3E50);
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color cardColor = Colors.white;
  static const Color textPrimaryColor = Color(0xFF2C3E50);
  static const Color textSecondaryColor = Color(0xFF7F8C8D);
  
  // 级别颜色
  static const Map<String, Color> levelColors = {
    '红色级别': Color(0xFFFF5722),
    '黄色级别': Color(0xFFFFC107),
    '蓝色级别': Color(0xFF2196F3),
    '绿色级别': Color(0xFF4CAF50),
  };
  
  // 字体大小
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 24.0;
  
  // 间距
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // 圆角
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  
  // 阴影
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;
  
  // 动画时长
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);
  
  // 应用信息
  static const String appName = '柯林斯大猫分级阅读';
  static const String appVersion = '1.0.0';
  
  // 资源路径
  static const String assetsImages = 'assets/images/';
  static const String assetsBooks = 'assets/books/';
  static const String assetsAudio = 'assets/audio/';
  
  // 级别列表
  static const List<String> readingLevels = [
    '全部',
    '红色级别',
    '黄色级别',
    '蓝色级别',
    '绿色级别',
  ];
  
  // 获取级别颜色
  static Color getLevelColor(String level) {
    return levelColors[level] ?? primaryColor;
  }
  
  // 主题数据
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.orange,
      primaryColor: primaryColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: Brightness.light,
      ),
      useMaterial3: true,
      fontFamily: 'PingFang SC',
      scaffoldBackgroundColor: backgroundColor,
      cardTheme: CardThemeData(
        color: cardColor,
        elevation: elevationMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
    );
  }
}
