import 'package:flutter/material.dart';
import '../models/book.dart';
import 'book_cover_image.dart';

/// 书籍卡片组件
class BookCard extends StatelessWidget {
  final Book book;
  final VoidCallback? onTap;
  final bool showLevel;

  const BookCard({
    super.key,
    required this.book,
    this.onTap,
    this.showLevel = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 封面图片
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  image: BookCoverDecorationImage(
                    imagePath: book.coverImage,
                    fit: BoxFit.cover,
                  ),
                ),
                child: showLevel
                    ? Align(
                        alignment: Alignment.topRight,
                        child: Container(
                          margin: const EdgeInsets.all(8),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: _getLevelColor(book.level),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            book.level,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      )
                    : null,
              ),
            ),
            
            // 书籍信息
            Container(
              height: 80, // 固定高度，确保所有卡片统一
              padding: const EdgeInsets.all(12),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 标题
                  Text(
                    book.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // 页数
                  Row(
                    children: [
                      Icon(
                        Icons.menu_book,
                        size: 14,
                        color: Colors.grey[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${book.pageCount}页',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getLevelColor(String level) {
    switch (level) {
      case '红色级别':
        return const Color(0xFFFF5722);
      case '黄色级别':
        return const Color(0xFFFFC107);
      case '蓝色级别':
        return const Color(0xFF2196F3);
      case '绿色级别':
        return const Color(0xFF4CAF50);
      default:
        return const Color(0xFFFF6B35);
    }
  }
}
