<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="reading_app">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>English Reading App</title>
  <link rel="manifest" href="manifest.json">

  <!-- PDF.js -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>

  <script>
    // PDF查看器函数
    function openPDFInNewWindow(pdfPath) {
      const pdfUrl = window.location.origin + '/assets/' + pdfPath.replace('assets/', '');
      const newWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');

      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Cars.pdf - PDF查看器</title>
          <style>
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { text-align: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 2px solid #4A90E2; }
            .pdf-info { background: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
            .download-btn { background: #4A90E2; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
            .download-btn:hover { background: #357abd; }
            .note { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>📚 Cars.pdf</h1>
              <p>English Graded Reading - Level 1</p>
            </div>

            <div class="pdf-info">
              <h3>📄 PDF文件信息</h3>
              <p><strong>文件名:</strong> Cars.pdf</p>
              <p><strong>路径:</strong> ${pdfPath}</p>
              <p><strong>类型:</strong> 英语分级阅读材料</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
              <a href="${pdfUrl}" class="download-btn" download="Cars.pdf">
                📥 下载 Cars.pdf
              </a>
            </div>

            <div class="note">
              <h4>💡 使用说明</h4>
              <p>1. 点击上方"下载 Cars.pdf"按钮下载PDF文件</p>
              <p>2. 使用您的PDF阅读器打开文件</p>
              <p>3. 返回主应用使用音频控制进行跟读练习</p>
              <p>4. 结合PDF内容和音频进行英语学习</p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <button onclick="window.close()" style="background: #6c757d; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">
                关闭窗口
              </button>
            </div>
          </div>
        </body>
        </html>
      `);

      newWindow.document.close();
    }

    // 将函数添加到全局作用域
    window.openPDFInNewWindow = openPDFInNewWindow;
  </script>
</head>
<body>
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
