"-Xallow-no-source-files" "-classpath" "D:\\workspace\\reading\\reading_app\\build\\audioplayers_android\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4ddd1defd3e0c4ef9252e30c34841d84\\transformed\\jetified-flutter_embedding_debug-1.0.0-dd93de6fb1776398bf586cbd477deade1391c7e4.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ec85a195f3d8caf00ca4b869cdb885fa\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\98271631e4f1a87237f04152c4c985d9\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\96c394fd60607c7dd5d7bcc0d47530e0\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\477756d298b6e925e25a98275dcc5fe9\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d33fd142c93f006da693497115ea39b8\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0a3134edf157ab3aba720d954425e31e\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\568a70bb3f483facf4d2d7d43ae6008c\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a369d4793d95f1cf7a87081c7b8c9fdd\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\20cfe6ef4b0a124cd0e1462452b12421\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c7fd0003b4c5f32b6401622484f58d3\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\94352ef56a2c0eb3126c4155ddbf4938\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cfe908072914cae0c03c4c0f407f54eb\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\baddc2ab01e31011d9a085c5afd8f90c\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\69a8da1683c718e294212bb079617d82\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c50491eb7fc85750cb06a9f9ac15ed5\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\80030f81c5c11797c21d6361ad2e793b\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4aba6fee28deb8137147f37b3bcd858b\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3c014aba32721c5d97646111590b969b\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1d7b96f62486d85fa5db6ade63609594\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fda9eaacc96b6b08a14a95252385c2ea\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7462755296592f32eb623f96e43bc6cf\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\21d7c1738b8ba5d31307b2e344230070\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1da56407fdb3c6ef1988316e93bb5572\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\111c41dbb68664070ad5d4c02bf57b79\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\09374e0b9974b89a2beea584c79ac353\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3f20299b68fb374241b3dbe7d7666543\\transformed\\jetified-kotlin-stdlib-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a7eec2535d584eb98d1143fa62ef9fa0\\transformed\\jetified-kotlin-stdlib-common-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6fe12c0a4100349fb6dd80cc0b5d1eee\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a5fc419f8a3a9500db8bf0a8a5eec4e4\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\00392a538534e18478a88d40e194cb43\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\831e9ba9d971996df52ca0cbff2f8ca8\\transformed\\jetified-relinker-1.4.5-api.jar;E:\\Android\\Sdk\\platforms\\android-35\\android.jar;E:\\Android\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\workspace\\reading\\reading_app\\build\\audioplayers_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "audioplayers_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\AudioContextAndroid.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\AudioplayersPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\FocusManager.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\MediaPlayerWrapper.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\PlayerWrapper.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\SoundPoolPlayer.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\player\\WrappedPlayer.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\PlayerMode.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\ReleaseMode.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\ByteDataSource.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\BytesSource.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\Source.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.flutter-io.cn\\audioplayers_android-5.2.1\\android\\src\\main\\kotlin\\xyz\\luan\\audioplayers\\source\\UrlSource.kt"