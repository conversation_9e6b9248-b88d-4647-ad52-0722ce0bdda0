class Book {
  final String id;
  final String title;
  final String description;
  final String coverImagePath;
  final String pdfPath;
  final String audioPath;
  final int totalPages;
  final String level; // 分级等级，如 "Level 1", "Level 2"
  final List<String> tags; // 标签，如 ["动物", "冒险"]
  final DateTime createdAt;
  final DateTime updatedAt;

  Book({
    required this.id,
    required this.title,
    required this.description,
    required this.coverImagePath,
    required this.pdfPath,
    required this.audioPath,
    required this.totalPages,
    required this.level,
    required this.tags,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      coverImagePath: json['coverImagePath'] as String,
      pdfPath: json['pdfPath'] as String,
      audioPath: json['audioPath'] as String,
      totalPages: json['totalPages'] as int,
      level: json['level'] as String,
      tags: List<String>.from(json['tags'] as List),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'coverImagePath': coverImagePath,
      'pdfPath': pdfPath,
      'audioPath': audioPath,
      'totalPages': totalPages,
      'level': level,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Book copyWith({
    String? id,
    String? title,
    String? description,
    String? coverImagePath,
    String? pdfPath,
    String? audioPath,
    int? totalPages,
    String? level,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Book(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      pdfPath: pdfPath ?? this.pdfPath,
      audioPath: audioPath ?? this.audioPath,
      totalPages: totalPages ?? this.totalPages,
      level: level ?? this.level,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Book && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Book(id: $id, title: $title, level: $level)';
  }
}
