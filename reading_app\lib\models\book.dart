/// 书籍数据模型
class Book {
  final String id;
  final String title;
  final String author;
  final String level;
  final String coverImage;
  final String pdfPath;
  final String audioPath;
  final String description;
  final int pageCount;
  final String levelColor;

  const Book({
    required this.id,
    required this.title,
    required this.author,
    required this.level,
    required this.coverImage,
    required this.pdfPath,
    required this.audioPath,
    required this.description,
    required this.pageCount,
    required this.levelColor,
  });

  /// 从JSON创建Book对象
  factory Book.fromJson(Map<String, dynamic> json) {
    return Book(
      id: json['id']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      author: json['author']?.toString() ?? '',
      level: json['level']?.toString() ?? '',
      coverImage: json['cover_image']?.toString() ?? '',
      pdfPath: json['pdf_path']?.toString() ?? '',
      audioPath: json['audio_path']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      pageCount: json['page_count'] as int? ?? 0,
      levelColor: json['level_color']?.toString() ?? getLevelColor(json['level']?.toString() ?? ''),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'author': author,
      'level': level,
      'cover_image': coverImage,
      'pdf_path': pdfPath,
      'audio_path': audioPath,
      'description': description,
      'page_count': pageCount,
      'level_color': levelColor,
    };
  }

  /// 获取级别颜色
  static String getLevelColor(String level) {
    switch (level) {
      case '红色级别':
        return '#FF5722';
      case '黄色级别':
        return '#FFC107';
      case '蓝色级别':
        return '#2196F3';
      case '绿色级别':
        return '#4CAF50';
      default:
        return '#FF6B35';
    }
  }

  /// 示例数据
  static List<Book> getSampleBooks() {
    return [
      const Book(
        id: 'cars_001',
        title: 'Cars',
        author: 'Collins Big Cat',
        level: '红色级别',
        coverImage: 'assets/images/Cars.jpg',
        pdfPath: 'assets/books/Cars.pdf',
        audioPath: 'assets/audio/Cars.wma',
        description: '一个关于汽车的精彩故事，适合初学者阅读。通过生动的图片和简单的文字，帮助孩子们认识各种汽车。',
        pageCount: 16,
        levelColor: '#FF5722',
      ),
      // 可以添加更多书籍...
    ];
  }
}
