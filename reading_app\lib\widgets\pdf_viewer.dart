import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:io';
import '../models/book.dart';

class PDFViewer extends StatefulWidget {
  final Book book;
  final int currentPage;
  final Function(int) onPageChanged;
  final VoidCallback? onTap;

  const PDFViewer({
    super.key,
    required this.book,
    required this.currentPage,
    required this.onPageChanged,
    this.onTap,
  });

  @override
  State<PDFViewer> createState() => _PDFViewerState();
}

class _PDFViewerState extends State<PDFViewer> {
  PDFViewController? _pdfController;
  bool _isReady = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    // 检查PDF文件是否存在
    if (widget.book.pdfPath.startsWith('assets/')) {
      return _buildAssetPDFViewer();
    } else {
      return _buildFilePDFViewer();
    }
  }

  Widget _buildAssetPDFViewer() {
    // 对于Web版本，显示PDF信息和页面导航
    return _buildWebPDFViewer();
  }

  Widget _buildFilePDFViewer() {
    final file = File(widget.book.pdfPath);
    
    if (!file.existsSync()) {
      return _buildErrorWidget('PDF文件不存在');
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: PDFView(
        filePath: widget.book.pdfPath,
        enableSwipe: true,
        swipeHorizontal: true,
        autoSpacing: false,
        pageFling: true,
        pageSnap: true,
        defaultPage: widget.currentPage - 1,
        fitPolicy: FitPolicy.BOTH,
        preventLinkNavigation: false,
        onRender: (pages) {
          setState(() {
            _isReady = true;
          });
        },
        onError: (error) {
          setState(() {
            _errorMessage = error.toString();
          });
        },
        onPageError: (page, error) {
          setState(() {
            _errorMessage = '页面 $page 加载失败: $error';
          });
        },
        onViewCreated: (PDFViewController pdfViewController) {
          _pdfController = pdfViewController;
        },
        onLinkHandler: (String? uri) {
          // 处理PDF中的链接
        },
        onPageChanged: (int? page, int? total) {
          if (page != null) {
            widget.onPageChanged(page + 1);
          }
        },
      ),
    );
  }

  Widget _buildPDFPlaceholder() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: double.infinity,
              height: MediaQuery.of(context).size.height * 0.7,
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.book.title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '第 ${widget.currentPage} 页 / 共 ${widget.book.totalPages} 页',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'PDF预览功能需要实际的PDF文件',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ),
            
            // 翻页按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: widget.currentPage > 1
                      ? () => widget.onPageChanged(widget.currentPage - 1)
                      : null,
                  icon: const Icon(Icons.navigate_before),
                  label: const Text('上一页'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: widget.currentPage < widget.book.totalPages
                      ? () => widget.onPageChanged(widget.currentPage + 1)
                      : null,
                  icon: const Icon(Icons.navigate_next),
                  label: const Text('下一页'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = null;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A90E2),
                  foregroundColor: Colors.white,
                ),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 跳转到指定页面
  Future<void> goToPage(int page) async {
    if (_pdfController != null) {
      await _pdfController!.setPage(page - 1);
    }
  }

  // Web版本的PDF查看器
  Widget _buildWebPDFViewer() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // PDF标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF4A90E2),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.picture_as_pdf,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.book.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'PDF文档 - 第${widget.currentPage}页 / 共${widget.book.totalPages}页',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // PDF内容区域 - 模拟PDF页面内容
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: _buildPageContent(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建页面内容
  Widget _buildPageContent() {
    // 根据当前页面显示不同的内容
    final pageContent = _getPageContent(widget.currentPage);

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 页面标题
          Center(
            child: Text(
              '${widget.book.title} - 第${widget.currentPage}页',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF4A90E2),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // 页面内容
          Text(
            pageContent,
            style: const TextStyle(
              fontSize: 16,
              height: 1.8,
              color: Colors.black87,
            ),
          ),

          const SizedBox(height: 32),

          // 页面底部信息
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.headphones,
                  color: Colors.grey[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '点击下方播放按钮开始音频，跟随音频进行阅读练习',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 获取页面内容
  String _getPageContent(int page) {
    // 为Cars书籍创建示例内容
    final Map<int, String> carsContent = {
      1: '''Welcome to the world of Cars!

Lightning McQueen is a race car who dreams of winning the Piston Cup Championship. He is fast, confident, and always ready for a race.

In this story, you will learn about friendship, teamwork, and the importance of helping others.

Let's begin our adventure with Lightning McQueen and his friends in Radiator Springs!''',

      2: '''Chapter 1: The Big Race

Lightning McQueen zoomed around the racetrack. "Ka-chiga!" he shouted as he passed other cars.

The crowd cheered loudly. McQueen was in the lead, but the race wasn't over yet.

"I am speed," McQueen said to himself. "I am going to win this race!"''',

      3: '''Chapter 2: Getting Lost

After the race, McQueen was driving to California for the big championship. But he got lost!

His trailer, Mack, was very tired. While Mack was sleeping, McQueen fell out of the trailer.

"Where am I?" McQueen wondered as he looked around the empty desert road.''',

      4: '''Chapter 3: Radiator Springs

McQueen found himself in a small town called Radiator Springs. The town looked old and quiet.

"This place is so boring," McQueen thought. "I need to get out of here quickly!"

But the townspeople had other plans for the famous race car.''',

      5: '''Chapter 4: Meeting New Friends

In Radiator Springs, McQueen met many interesting cars:

• Mater - a friendly tow truck who became his best friend
• Sally - a smart and kind Porsche
• Doc Hudson - a wise old car with a secret past

Each friend taught McQueen something important about life.''',
    };

    return carsContent[page] ?? '''第${page}页

继续阅读Cars的精彩故事...

Lightning McQueen在Radiator Springs学会了友谊和团队合作的重要性。

每一页都有新的冒险等着你去发现！

使用下方的音频控制来听故事，然后练习跟读。''';
  }
}
