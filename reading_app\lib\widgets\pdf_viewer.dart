import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:io';
import '../models/book.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

class PDFViewer extends StatefulWidget {
  final Book book;
  final int currentPage;
  final Function(int) onPageChanged;
  final VoidCallback? onTap;

  const PDFViewer({
    super.key,
    required this.book,
    required this.currentPage,
    required this.onPageChanged,
    this.onTap,
  });

  @override
  State<PDFViewer> createState() => _PDFViewerState();
}

class _PDFViewerState extends State<PDFViewer> {
  PDFViewController? _pdfController;
  bool _isReady = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    // 检查PDF文件是否存在
    if (widget.book.pdfPath.startsWith('assets/')) {
      return _buildAssetPDFViewer();
    } else {
      return _buildFilePDFViewer();
    }
  }

  Widget _buildAssetPDFViewer() {
    // 对于移动端，从assets加载PDF文件
    return FutureBuilder<String>(
      future: _copyAssetToLocal(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载PDF文件...'),
              ],
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return _buildPDFPlaceholder();
        }

        return PDFView(
          filePath: snapshot.data!,
          enableSwipe: true,
          swipeHorizontal: true,
          autoSpacing: false,
          pageFling: false,
          onRender: (pages) {
            setState(() {
              _totalPages = pages ?? 0;
            });
          },
          onError: (error) {
            setState(() {
              _errorMessage = error.toString();
            });
          },
          onPageError: (page, error) {
            setState(() {
              _errorMessage = 'Page $page: $error';
            });
          },
          onViewCreated: (PDFViewController pdfViewController) {
            _pdfController = pdfViewController;
          },
          onPageChanged: (int? page, int? total) {
            if (page != null && widget.onPageChanged != null) {
              widget.onPageChanged!(page + 1);
            }
          },
        );
      },
    );
  }

  // 将assets中的PDF文件复制到本地临时目录
  Future<String> _copyAssetToLocal() async {
    try {
      final byteData = await rootBundle.load(widget.book.pdfPath);
      final file = File('${(await getTemporaryDirectory()).path}/${widget.book.title}.pdf');
      await file.writeAsBytes(byteData.buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
      return file.path;
    } catch (e) {
      throw Exception('无法加载PDF文件: $e');
    }
  }

  Widget _buildFilePDFViewer() {
    final file = File(widget.book.pdfPath);
    
    if (!file.existsSync()) {
      return _buildErrorWidget('PDF文件不存在');
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: PDFView(
        filePath: widget.book.pdfPath,
        enableSwipe: true,
        swipeHorizontal: true,
        autoSpacing: false,
        pageFling: true,
        pageSnap: true,
        defaultPage: widget.currentPage - 1,
        fitPolicy: FitPolicy.BOTH,
        preventLinkNavigation: false,
        onRender: (pages) {
          setState(() {
            _isReady = true;
          });
        },
        onError: (error) {
          setState(() {
            _errorMessage = error.toString();
          });
        },
        onPageError: (page, error) {
          setState(() {
            _errorMessage = '页面 $page 加载失败: $error';
          });
        },
        onViewCreated: (PDFViewController pdfViewController) {
          _pdfController = pdfViewController;
        },
        onLinkHandler: (String? uri) {
          // 处理PDF中的链接
        },
        onPageChanged: (int? page, int? total) {
          if (page != null) {
            widget.onPageChanged(page + 1);
          }
        },
      ),
    );
  }

  Widget _buildPDFPlaceholder() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: double.infinity,
              height: MediaQuery.of(context).size.height * 0.7,
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.book.title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '第 ${widget.currentPage} 页 / 共 ${widget.book.totalPages} 页',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'PDF预览功能需要实际的PDF文件',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ),
            
            // 翻页按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: widget.currentPage > 1
                      ? () => widget.onPageChanged(widget.currentPage - 1)
                      : null,
                  icon: const Icon(Icons.navigate_before),
                  label: const Text('上一页'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: widget.currentPage < widget.book.totalPages
                      ? () => widget.onPageChanged(widget.currentPage + 1)
                      : null,
                  icon: const Icon(Icons.navigate_next),
                  label: const Text('下一页'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = null;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A90E2),
                  foregroundColor: Colors.white,
                ),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 跳转到指定页面
  Future<void> goToPage(int page) async {
    if (_pdfController != null) {
      await _pdfController!.setPage(page - 1);
    }
  }






}
