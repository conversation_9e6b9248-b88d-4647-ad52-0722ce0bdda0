import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:io';
import '../models/book.dart';
import 'dart:html' as html;
import 'dart:js' as js;

class PDFViewer extends StatefulWidget {
  final Book book;
  final int currentPage;
  final Function(int) onPageChanged;
  final VoidCallback? onTap;

  const PDFViewer({
    super.key,
    required this.book,
    required this.currentPage,
    required this.onPageChanged,
    this.onTap,
  });

  @override
  State<PDFViewer> createState() => _PDFViewerState();
}

class _PDFViewerState extends State<PDFViewer> {
  PDFViewController? _pdfController;
  bool _isReady = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    // 检查PDF文件是否存在
    if (widget.book.pdfPath.startsWith('assets/')) {
      return _buildAssetPDFViewer();
    } else {
      return _buildFilePDFViewer();
    }
  }

  Widget _buildAssetPDFViewer() {
    // 对于Web版本，显示PDF信息和页面导航
    return _buildWebPDFViewer();
  }

  Widget _buildFilePDFViewer() {
    final file = File(widget.book.pdfPath);
    
    if (!file.existsSync()) {
      return _buildErrorWidget('PDF文件不存在');
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: PDFView(
        filePath: widget.book.pdfPath,
        enableSwipe: true,
        swipeHorizontal: true,
        autoSpacing: false,
        pageFling: true,
        pageSnap: true,
        defaultPage: widget.currentPage - 1,
        fitPolicy: FitPolicy.BOTH,
        preventLinkNavigation: false,
        onRender: (pages) {
          setState(() {
            _isReady = true;
          });
        },
        onError: (error) {
          setState(() {
            _errorMessage = error.toString();
          });
        },
        onPageError: (page, error) {
          setState(() {
            _errorMessage = '页面 $page 加载失败: $error';
          });
        },
        onViewCreated: (PDFViewController pdfViewController) {
          _pdfController = pdfViewController;
        },
        onLinkHandler: (String? uri) {
          // 处理PDF中的链接
        },
        onPageChanged: (int? page, int? total) {
          if (page != null) {
            widget.onPageChanged(page + 1);
          }
        },
      ),
    );
  }

  Widget _buildPDFPlaceholder() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: double.infinity,
              height: MediaQuery.of(context).size.height * 0.7,
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    size: 80,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    widget.book.title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '第 ${widget.currentPage} 页 / 共 ${widget.book.totalPages} 页',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'PDF预览功能需要实际的PDF文件',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[400],
                    ),
                  ),
                ],
              ),
            ),
            
            // 翻页按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: widget.currentPage > 1
                      ? () => widget.onPageChanged(widget.currentPage - 1)
                      : null,
                  icon: const Icon(Icons.navigate_before),
                  label: const Text('上一页'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: widget.currentPage < widget.book.totalPages
                      ? () => widget.onPageChanged(widget.currentPage + 1)
                      : null,
                  icon: const Icon(Icons.navigate_next),
                  label: const Text('下一页'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.white,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[300],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.red[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _errorMessage = null;
                  });
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4A90E2),
                  foregroundColor: Colors.white,
                ),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 跳转到指定页面
  Future<void> goToPage(int page) async {
    if (_pdfController != null) {
      await _pdfController!.setPage(page - 1);
    }
  }

  // Web版本的PDF查看器
  Widget _buildWebPDFViewer() {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // PDF标题栏
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: Color(0xFF4A90E2),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.picture_as_pdf,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.book.title,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'PDF文档 - 第${widget.currentPage}页 / 共${widget.book.totalPages}页',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // 打开原始PDF按钮
                  IconButton(
                    onPressed: () => _openOriginalPDF(),
                    icon: const Icon(
                      Icons.open_in_new,
                      color: Colors.white,
                      size: 20,
                    ),
                    tooltip: '在新窗口打开原始PDF',
                  ),
                ],
              ),
            ),
            // PDF内容区域 - 嵌入真实PDF
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: _buildPDFEmbed(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  // 构建PDF嵌入视图
  Widget _buildPDFEmbed() {
    // 在Web环境中使用iframe嵌入PDF
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Column(
        children: [
          // PDF工具栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '真实PDF文件: ${widget.book.title}.pdf',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
                TextButton.icon(
                  onPressed: () => _openOriginalPDF(),
                  icon: const Icon(Icons.open_in_new, size: 16),
                  label: const Text('在新窗口打开'),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF4A90E2),
                    textStyle: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ),
          // PDF预览区域
          Expanded(
            child: Container(
              width: double.infinity,
              color: Colors.grey[50],
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.picture_as_pdf,
                    size: 80,
                    color: Colors.red[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '${widget.book.title}.pdf',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '第 ${widget.currentPage} 页 / 共 ${widget.book.totalPages} 页',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Container(
                    padding: const EdgeInsets.all(16),
                    margin: const EdgeInsets.symmetric(horizontal: 32),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.touch_app,
                          color: Colors.blue[600],
                          size: 32,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          '点击上方"在新窗口打开"按钮',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[800],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '在新窗口中查看完整的Cars.pdf文件内容\n同时可以在这里使用音频控制进行跟读练习',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 打开原始PDF文件
  void _openOriginalPDF() {
    try {
      // 调用JavaScript函数打开PDF
      js.context.callMethod('openPDFInNewWindow', [widget.book.pdfPath]);

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text('正在新窗口中打开Cars.pdf文件...'),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('打开PDF失败: $e');

      // 显示错误提示和备用方案
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.info, color: Colors.white),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text('PDF文件信息'),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text('文件路径: ${widget.book.pdfPath}'),
                const Text('请手动访问PDF文件或下载查看'),
              ],
            ),
            backgroundColor: const Color(0xFF4A90E2),
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: '了解',
              textColor: Colors.white,
              onPressed: () {},
            ),
          ),
        );
      }
    }
  }
}
