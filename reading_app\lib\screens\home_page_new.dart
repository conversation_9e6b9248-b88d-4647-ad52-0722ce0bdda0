import 'package:flutter/material.dart';
import 'reading_page.dart';
import '../models/book.dart';
import '../widgets/book_card.dart';
import '../widgets/book_cover_image.dart';
import '../constants/app_constants.dart';
import '../services/api_service.dart';

/// 首页 - 新设计
class HomePageNew extends StatefulWidget {
  const HomePageNew({super.key});

  @override
  State<HomePageNew> createState() => _HomePageNewState();
}

class _HomePageNewState extends State<HomePageNew> {
  final TextEditingController _searchController = TextEditingController();
  final ApiService _apiService = ApiService();

  List<Book> _allBooks = [];
  List<Book> _recentBooks = [];
  List<Book> _recommendedBooks = [];
  List<Book> _searchResults = [];

  bool _isLoading = false;
  bool _isSearching = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  /// 加载书籍数据
  Future<void> _loadBooks() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 并行加载推荐书籍和最近阅读
      final results = await Future.wait([
        _apiService.getRecommendedBooks(limit: 10),
        _apiService.getRecentBooks(limit: 4),
      ]);

      setState(() {
        _recommendedBooks = results[0];
        _recentBooks = results[1];
        _allBooks = [..._recommendedBooks, ..._recentBooks];
        _isLoading = false;
      });
    } catch (e) {
      print('加载书籍数据失败: $e');
      setState(() {
        _isLoading = false;
        // 使用本地示例数据作为备用
        _allBooks = Book.getSampleBooks();
        _recentBooks = _allBooks.take(4).toList();
        _recommendedBooks = _allBooks;
      });
    }
  }

  /// 搜索书籍
  Future<void> _searchBooks(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
        _searchQuery = '';
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _searchQuery = query;
    });

    try {
      final results = await _apiService.searchBooks(query, limit: 20);
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      print('搜索失败: $e');
      setState(() {
        _isSearching = false;
        _searchResults = [];
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          '柯林斯大猫分级阅读',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppConstants.primaryColor,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 欢迎卡片
            Container(
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              padding: const EdgeInsets.all(AppConstants.paddingLarge),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppConstants.primaryColor,
                    AppConstants.primaryColor.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '欢迎回来！',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeXLarge,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingSmall),
                        Text(
                          '继续你的英语阅读之旅',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeMedium,
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.auto_stories,
                    size: 48,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ],
              ),
            ),

            // 搜索框
            Container(
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search,
                    color: AppConstants.primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      onChanged: (value) {
                        // 延迟搜索，避免频繁请求
                        Future.delayed(const Duration(milliseconds: 500), () {
                          if (_searchController.text == value) {
                            _searchBooks(value);
                          }
                        });
                      },
                      onSubmitted: _searchBooks,
                      decoration: InputDecoration(
                        hintText: '搜索书籍...',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                        suffixIcon: _searchController.text.isNotEmpty
                            ? IconButton(
                                icon: const Icon(Icons.clear),
                                onPressed: () {
                                  _searchController.clear();
                                  _searchBooks('');
                                },
                              )
                            : null,
                      ),
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                        color: AppConstants.textPrimaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 根据搜索状态显示不同内容
            if (_searchQuery.isNotEmpty) ...[
              // 搜索结果区域
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                child: Text(
                  '搜索结果 "${_searchQuery}"',
                  style: TextStyle(
                    fontSize: AppConstants.fontSizeXLarge,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.textPrimaryColor,
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),

              if (_isSearching)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(AppConstants.paddingLarge),
                    child: CircularProgressIndicator(),
                  ),
                )
              else if (_searchResults.isEmpty)
                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.paddingLarge),
                    child: Column(
                      children: [
                        Icon(
                          Icons.search_off,
                          size: 64,
                          color: AppConstants.textSecondaryColor,
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        Text(
                          '没有找到相关书籍',
                          style: TextStyle(
                            fontSize: AppConstants.fontSizeLarge,
                            color: AppConstants.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else
                // 搜索结果网格
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                  child: GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75, // 调整比例，给名称区域更多空间
                      crossAxisSpacing: AppConstants.paddingMedium,
                      mainAxisSpacing: AppConstants.paddingMedium,
                    ),
                    itemCount: _searchResults.length,
                    itemBuilder: (context, index) {
                      return _buildBookCard(_searchResults[index]);
                    },
                  ),
                ),
            ] else ...[
              // 默认首页内容
              if (_isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.all(AppConstants.paddingLarge),
                    child: CircularProgressIndicator(),
                  ),
                )
              else ...[
                // 推荐书籍区域
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                  child: Text(
                    '推荐绘本',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeXLarge,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.paddingSmall),

                // 推荐书籍横向滚动
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                    itemCount: _recommendedBooks.length,
                    itemBuilder: (context, index) {
                      final book = _recommendedBooks[index];
                      return Container(
                        width: 140,
                        margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                        child: _buildBookCard(book),
                      );
                    },
                  ),
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // 最近阅读区域
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
                  child: Text(
                    '最近阅读',
                    style: TextStyle(
                      fontSize: AppConstants.fontSizeXLarge,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.textPrimaryColor,
                    ),
                  ),
                ),
            const SizedBox(height: AppConstants.paddingSmall),
            
            // 最近阅读横向滚动
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                itemCount: _recentBooks.length,
                itemBuilder: (context, index) {
                  final book = _recentBooks[index];
                  return Container(
                    width: 140,
                    margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                    child: _buildBookCard(book),
                  );
                },
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBookCard(Book book) {
    return GestureDetector(
      onTap: () async {
        // 记录阅读历史
        await _apiService.recordReadingHistory(book.id);

        // 导航到阅读页面
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReadingPage(book: book),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 封面图片
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    image: BookCoverDecorationImage(
                      imagePath: book.coverImage,
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Container(
                        margin: const EdgeInsets.all(8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.getLevelColor(book.level),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          book.level,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              
              // 书籍信息
              Container(
                width: double.infinity,
                constraints: const BoxConstraints(
                  minHeight: 50,
                  maxHeight: 70,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppConstants.paddingSmall,
                  vertical: 6,
                ),
                decoration: const BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(AppConstants.radiusMedium),
                    bottomRight: Radius.circular(AppConstants.radiusMedium),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        book.title,
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeMedium,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.textPrimaryColor,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      book.level,
                      style: TextStyle(
                        fontSize: AppConstants.fontSizeSmall,
                        color: AppConstants.textSecondaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
