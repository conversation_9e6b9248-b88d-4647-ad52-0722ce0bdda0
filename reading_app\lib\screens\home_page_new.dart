import 'package:flutter/material.dart';
import 'reading_page.dart';
import '../models/book.dart';
import '../widgets/book_card.dart';
import '../constants/app_constants.dart';

/// 首页 - 新设计
class HomePageNew extends StatefulWidget {
  const HomePageNew({super.key});

  @override
  State<HomePageNew> createState() => _HomePageNewState();
}

class _HomePageNewState extends State<HomePageNew> {
  final TextEditingController _searchController = TextEditingController();
  List<Book> _allBooks = [];
  List<Book> _recentBooks = [];
  List<Book> _recommendedBooks = [];

  @override
  void initState() {
    super.initState();
    _loadBooks();
  }

  void _loadBooks() {
    _allBooks = Book.getSampleBooks();
    // 模拟最近阅读的书籍（取前4本）
    _recentBooks = _allBooks.take(4).toList();
    // 模拟推荐书籍（取所有书籍）
    _recommendedBooks = _allBooks;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF4FC3F7), // 蓝色背景，类似图片
      appBar: AppBar(
        title: const Text(
          '柯林斯大猫分级阅读',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF4FC3F7),
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 搜索框
            Container(
              margin: const EdgeInsets.all(AppConstants.paddingMedium),
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              decoration: BoxDecoration(
                color: const Color(0xFFFFF8E1), // 浅黄色背景，类似图片
                borderRadius: BorderRadius.circular(30),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search,
                    color: Colors.grey[600],
                    size: 24,
                  ),
                  const SizedBox(width: AppConstants.paddingSmall),
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        hintText: '搜索书籍...',
                        border: InputBorder.none,
                        hintStyle: TextStyle(
                          color: Colors.grey,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                      style: const TextStyle(
                        fontSize: AppConstants.fontSizeMedium,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 推荐书籍区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              child: const Text(
                'Recommended Books',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFDD835), // 黄色标题
                ),
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            
            // 推荐书籍横向滚动
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                itemCount: _recommendedBooks.length,
                itemBuilder: (context, index) {
                  final book = _recommendedBooks[index];
                  return Container(
                    width: 140,
                    margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                    child: _buildBookCard(book),
                  );
                },
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),

            // 最近阅读区域
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingMedium),
              child: const Text(
                'Recently Read',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFFFDD835), // 黄色标题
                ),
              ),
            ),
            const SizedBox(height: AppConstants.paddingSmall),
            
            // 最近阅读横向滚动
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                itemCount: _recentBooks.length,
                itemBuilder: (context, index) {
                  final book = _recentBooks[index];
                  return Container(
                    width: 140,
                    margin: const EdgeInsets.symmetric(horizontal: AppConstants.paddingSmall),
                    child: _buildBookCard(book),
                  );
                },
              ),
            ),

            const SizedBox(height: AppConstants.paddingLarge),
          ],
        ),
      ),
    );
  }

  Widget _buildBookCard(Book book) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ReadingPage(book: book),
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 封面图片
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage(book.coverImage),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                    child: Align(
                      alignment: Alignment.topRight,
                      child: Container(
                        margin: const EdgeInsets.all(8),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: AppConstants.getLevelColor(book.level),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          book.level,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              
              // 书籍信息
              Container(
                padding: const EdgeInsets.all(8),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      book.title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: AppConstants.textPrimaryColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
