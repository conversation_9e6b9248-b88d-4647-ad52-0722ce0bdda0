import 'package:flutter/material.dart';
import '../models/book.dart';
import '../models/reading_progress.dart';
import '../models/audio_state.dart';
import '../services/book_service.dart';
import '../services/audio_service.dart';

class AppProvider with ChangeNotifier {
  final BookService _bookService = BookService();
  final AudioService _audioService = AudioService();

  // 书籍相关状态
  List<Book> _books = [];
  List<Book> _filteredBooks = [];
  Book? _currentBook;
  ReadingProgress? _currentProgress;
  bool _isLoading = false;
  String? _errorMessage;

  // 音频相关状态
  AudioState _audioState = AudioState();

  // PDF相关状态
  int _currentPage = 1;
  int _totalPages = 0;

  // 筛选和搜索状态
  String _selectedLevel = '';
  String _searchQuery = '';

  // Getters
  List<Book> get books => _books;
  List<Book> get filteredBooks => _filteredBooks;
  Book? get currentBook => _currentBook;
  ReadingProgress? get currentProgress => _currentProgress;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  AudioState get audioState => _audioState;
  int get currentPage => _currentPage;
  int get totalPages => _totalPages;
  String get selectedLevel => _selectedLevel;
  String get searchQuery => _searchQuery;

  AppProvider() {
    _initializeAudioService();
  }

  void _initializeAudioService() {
    _audioService.initialize();
    _audioService.stateStream.listen((AudioState state) {
      _audioState = state;
      notifyListeners();
    });
  }

  // 加载所有书籍
  Future<void> loadBooks() async {
    _setLoading(true);
    try {
      _books = await _bookService.getAllBooks();
      _applyFilters();
      _clearError();
    } catch (e) {
      _setError('加载书籍失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  // 搜索书籍
  Future<void> searchBooks(String query) async {
    _searchQuery = query;
    if (query.isEmpty) {
      _applyFilters();
    } else {
      _setLoading(true);
      try {
        final searchResults = await _bookService.searchBooks(query);
        _filteredBooks = searchResults;
        _clearError();
      } catch (e) {
        _setError('搜索失败: $e');
      } finally {
        _setLoading(false);
      }
    }
    notifyListeners();
  }

  // 按等级筛选
  Future<void> filterByLevel(String level) async {
    _selectedLevel = level;
    _applyFilters();
    notifyListeners();
  }

  // 应用筛选条件
  void _applyFilters() {
    _filteredBooks = _books;
    
    if (_selectedLevel.isNotEmpty) {
      _filteredBooks = _filteredBooks
          .where((book) => book.level == _selectedLevel)
          .toList();
    }
    
    if (_searchQuery.isNotEmpty) {
      final lowerQuery = _searchQuery.toLowerCase();
      _filteredBooks = _filteredBooks.where((book) => 
        book.title.toLowerCase().contains(lowerQuery) ||
        book.description.toLowerCase().contains(lowerQuery) ||
        book.tags.any((tag) => tag.toLowerCase().contains(lowerQuery))
      ).toList();
    }
  }

  // 选择当前书籍
  Future<void> selectBook(Book book) async {
    _currentBook = book;
    _totalPages = book.totalPages;
    
    // 加载阅读进度
    try {
      _currentProgress = await _bookService.getReadingProgress(book.id);
      if (_currentProgress != null) {
        _currentPage = _currentProgress!.currentPage;
      }
    } catch (e) {
      _setError('加载阅读进度失败: $e');
    }
    
    notifyListeners();
  }

  // 更新当前页面
  void updateCurrentPage(int page) {
    _currentPage = page.clamp(1, _totalPages);
    _updateReadingProgress();
    notifyListeners();
  }

  // 下一页
  void nextPage() {
    if (_currentPage < _totalPages) {
      updateCurrentPage(_currentPage + 1);
    }
  }

  // 上一页
  void previousPage() {
    if (_currentPage > 1) {
      updateCurrentPage(_currentPage - 1);
    }
  }

  // 更新阅读进度
  Future<void> _updateReadingProgress() async {
    if (_currentBook == null || _currentProgress == null) return;

    final updatedProgress = _currentProgress!.copyWith(
      currentPage: _currentPage,
      audioPosition: _audioState.currentPosition.inSeconds.toDouble(),
      lastReadAt: DateTime.now(),
      isCompleted: _currentPage >= _totalPages,
    );

    _currentProgress = updatedProgress;
    
    try {
      await _bookService.updateReadingProgress(updatedProgress);
    } catch (e) {
      _setError('保存阅读进度失败: $e');
    }
  }

  // 音频控制方法
  Future<void> playAudio() async {
    if (_currentBook?.audioPath != null) {
      await _audioService.playAudio(_currentBook!.audioPath);
    }
  }

  Future<void> pauseAudio() async {
    await _audioService.pauseAudio();
  }

  Future<void> resumeAudio() async {
    await _audioService.resumeAudio();
  }

  Future<void> stopAudio() async {
    await _audioService.stopAudio();
  }

  Future<void> seekAudio(Duration position) async {
    await _audioService.seekTo(position);
  }

  Future<void> setVolume(double volume) async {
    await _audioService.setVolume(volume);
  }

  Future<void> setPlaybackSpeed(double speed) async {
    await _audioService.setPlaybackSpeed(speed);
  }

  // 录音控制方法
  Future<String?> startRecording() async {
    return await _audioService.startRecording();
  }

  Future<String?> stopRecording() async {
    final recordingPath = await _audioService.stopRecording();
    if (recordingPath != null && _currentProgress != null) {
      final updatedProgress = _currentProgress!.copyWith(
        recordingPaths: [..._currentProgress!.recordingPaths, recordingPath],
      );
      _currentProgress = updatedProgress;
      await _bookService.updateReadingProgress(updatedProgress);
    }
    return recordingPath;
  }

  // 播放录音
  Future<void> playRecording(String recordingPath) async {
    await _audioService.playAudio(recordingPath);
  }

  // 工具方法
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // 清除所有状态
  void clearAll() {
    _currentBook = null;
    _currentProgress = null;
    _currentPage = 1;
    _totalPages = 0;
    _selectedLevel = '';
    _searchQuery = '';
    _filteredBooks = _books;
    _clearError();
    notifyListeners();
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }
}
