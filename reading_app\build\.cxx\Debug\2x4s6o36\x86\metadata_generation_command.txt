                        -HD:\flutter\packages\flutter_tools\gradle\src\main\scripts
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-D<PERSON>DROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=E:\Android\Sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=E:\Android\Sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=E:\Android\Sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=E:\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\workspace\reading\reading_app\build\app\intermediates\cxx\Debug\2x4s6o36\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\workspace\reading\reading_app\build\app\intermediates\cxx\Debug\2x4s6o36\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BD:\workspace\reading\reading_app\build\.cxx\Debug\2x4s6o36\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2