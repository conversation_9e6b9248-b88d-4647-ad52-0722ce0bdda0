import 'package:flutter/material.dart';

class LevelFilter extends StatelessWidget {
  final String selectedLevel;
  final Function(String) onLevelSelected;

  const LevelFilter({
    super.key,
    required this.selectedLevel,
    required this.onLevelSelected,
  });

  @override
  Widget build(BuildContext context) {
    final levels = ['全部', 'Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5'];

    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: levels.length,
        itemBuilder: (context, index) {
          final level = levels[index];
          final isSelected = (level == '全部' && selectedLevel.isEmpty) || 
                           (level == selectedLevel);

          return Padding(
            padding: EdgeInsets.only(
              left: index == 0 ? 0 : 8,
              right: index == levels.length - 1 ? 0 : 0,
            ),
            child: FilterChip(
              label: Text(
                level,
                style: TextStyle(
                  color: isSelected ? Colors.white : const Color(0xFF4A90E2),
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (level == '全部') {
                  onLevelSelected('');
                } else {
                  onLevelSelected(level);
                }
              },
              backgroundColor: Colors.white,
              selectedColor: _getLevelColor(level),
              checkmarkColor: Colors.white,
              side: BorderSide(
                color: isSelected ? _getLevelColor(level) : const Color(0xFF4A90E2),
                width: 1.5,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
          );
        },
      ),
    );
  }

  Color _getLevelColor(String level) {
    switch (level.toLowerCase()) {
      case 'level 1':
        return const Color(0xFF27AE60); // 绿色
      case 'level 2':
        return const Color(0xFF3498DB); // 蓝色
      case 'level 3':
        return const Color(0xFFF39C12); // 橙色
      case 'level 4':
        return const Color(0xFFE74C3C); // 红色
      case 'level 5':
        return const Color(0xFF9B59B6); // 紫色
      default:
        return const Color(0xFF4A90E2); // 默认蓝色
    }
  }
}
