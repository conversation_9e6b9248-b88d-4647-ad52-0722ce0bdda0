# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.20.21032501-MSVC_2)

# We name the project and the target for the ExternalProject_Add() call
# to something that will highlight to the user what we are working on if
# something goes wrong and an error message is produced.

project(nuget-populate NONE)



include(ExternalProject)
ExternalProject_Add(nuget-populate
                     "UPDATE_DISCONNECTED" "False" "URL" "https://dist.nuget.org/win-x86-commandline/v6.5.0/nuget.exe" "URL_HASH" "SHA256=d5fce5185de92b7356ea9264b997a620e35c6f6c3c061e471e0dc3a84b3d74fd" DOWNLOAD_NO_EXTRACT YES
                    SOURCE_DIR          "D:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-src"
                    BINARY_DIR          "D:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-build"
                    CONFIGURE_COMMAND   ""
                    BUILD_COMMAND       ""
                    INSTALL_COMMAND     ""
                    TEST_COMMAND        ""
                    USES_TERMINAL_DOWNLOAD  YES
                    USES_TERMINAL_UPDATE    YES
)


ExternalProject_Get_Property(nuget-populate DOWNLOADED_FILE)
get_filename_component(dlFileName "${DOWNLOADED_FILE}" NAME)

ExternalProject_Add_Step(nuget-populate copyfile
  COMMAND    "C:/Program Files (x86)/Microsoft Visual Studio/2019/BuildTools/Common7/IDE/CommonExtensions/Microsoft/CMake/CMake/bin/cmake.exe" -E copy_if_different
             "<DOWNLOADED_FILE>" "D:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-src"
  DEPENDEES  patch
  DEPENDERS  configure
  BYPRODUCTS "D:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-src/${dlFileName}"
  COMMENT    "Copying file to SOURCE_DIR"
)

