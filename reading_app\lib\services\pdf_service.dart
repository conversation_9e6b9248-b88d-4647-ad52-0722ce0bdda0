import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:http/http.dart' as http;

/// PDF服务类 - 处理PDF相关操作
class PDFService {
  static final PDFService _instance = PDFService._internal();
  factory PDFService() => _instance;
  PDFService._internal();

  /// 加载PDF文件（支持本地资源和网络URL）
  Future<String?> loadPDF(String pdfPath) async {
    try {
      // 判断是否为网络URL
      if (pdfPath.startsWith('http://') || pdfPath.startsWith('https://')) {
        return await _loadPDFFromNetwork(pdfPath);
      } else {
        return await _loadPDFFromAssets(pdfPath);
      }
    } catch (e) {
      print('PDF加载失败: $e');
      return null;
    }
  }

  /// 从assets加载PDF到临时目录
  Future<String?> _loadPDFFromAssets(String assetPath) async {
    try {
      final byteData = await rootBundle.load(assetPath);
      final fileName = assetPath.split('/').last;
      final file = File('${(await getTemporaryDirectory()).path}/$fileName');

      await file.writeAsBytes(byteData.buffer.asUint8List(
        byteData.offsetInBytes,
        byteData.lengthInBytes,
      ));

      return file.path;
    } catch (e) {
      print('从assets加载PDF失败: $e');
      return null;
    }
  }

  /// 从网络下载PDF到临时目录（支持缓存）
  Future<String?> _loadPDFFromNetwork(String networkUrl) async {
    try {
      final fileName = networkUrl.split('/').last;
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/$fileName');

      // 检查缓存文件是否存在
      if (await file.exists()) {
        print('使用缓存的PDF文件: ${file.path}');
        return file.path;
      }

      print('开始下载PDF: $networkUrl');

      final response = await http.get(Uri.parse(networkUrl));

      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);

        print('PDF下载成功: ${file.path}');
        return file.path;
      } else {
        print('PDF下载失败: HTTP ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('从网络下载PDF失败: $e');
      return null;
    }
  }

  /// 检查PDF文件是否存在
  Future<bool> isPDFExists(String filePath) async {
    try {
      final file = File(filePath);
      return await file.exists();
    } catch (e) {
      return false;
    }
  }

  /// 清理PDF缓存
  Future<void> clearPDFCache() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();

      for (final file in files) {
        if (file is File && file.path.toLowerCase().endsWith('.pdf')) {
          await file.delete();
          print('删除缓存文件: ${file.path}');
        }
      }

      print('PDF缓存清理完成');
    } catch (e) {
      print('清理PDF缓存失败: $e');
    }
  }

  /// 获取缓存大小
  Future<int> getCacheSize() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();
      int totalSize = 0;

      for (final file in files) {
        if (file is File && file.path.toLowerCase().endsWith('.pdf')) {
          totalSize += await file.length();
        }
      }

      return totalSize;
    } catch (e) {
      print('获取缓存大小失败: $e');
      return 0;
    }
  }

  /// 获取PDF文件大小
  Future<int> getPDFSize(String filePath) async {
    try {
      final file = File(filePath);
      return await file.length();
    } catch (e) {
      return 0;
    }
  }

  /// 清理临时PDF文件
  Future<void> cleanupTempPDFs() async {
    try {
      final tempDir = await getTemporaryDirectory();
      final files = tempDir.listSync();
      
      for (final file in files) {
        if (file.path.endsWith('.pdf')) {
          await file.delete();
        }
      }
    } catch (e) {
      print('清理临时文件失败: $e');
    }
  }
}
