# API测试指南

## 🎯 当前状态

您已经成功更新了API服务的URL为：`https://books.jiudianlianxian.com/api`

应用现在正在尝试从以下接口获取数据：
- `https://books.jiudianlianxian.com/api/books.json` - 获取所有书籍、推荐书籍、最近阅读

## 📋 需要实现的API接口

### 1. 主要数据接口

**URL**: `https://books.jiudianlianxian.com/api/books.json`

**方法**: GET

**响应格式**:
```json
{
  "success": true,
  "message": "获取书籍列表成功",
  "data": [
    {
      "id": "cars_001",
      "title": "Cars",
      "author": "Collins Big Cat",
      "level": "红色级别",
      "cover_image": "https://books.jiudianlianxian.com/covers/Cars.jpg",
      "pdf_path": "https://books.jiudianlianxian.com/pdf/Cars.pdf",
      "audio_path": "https://books.jiudianlianxian.com/audio/Cars.mp3",
      "description": "一个关于汽车的精彩故事，适合初学者阅读。",
      "page_count": 16,
      "level_color": "#FF5722"
    },
    {
      "id": "animals_002",
      "title": "Animals",
      "author": "Collins Big Cat",
      "level": "黄色级别",
      "cover_image": "https://books.jiudianlianxian.com/covers/Animals.jpg",
      "pdf_path": "https://books.jiudianlianxian.com/pdf/Animals.pdf",
      "audio_path": "https://books.jiudianlianxian.com/audio/Animals.mp3",
      "description": "探索动物世界的精彩故事。",
      "page_count": 20,
      "level_color": "#FFC107"
    }
  ]
}
```

### 2. 搜索接口

**URL**: `https://books.jiudianlianxian.com/api/books/search`

**方法**: GET

**参数**: 
- `q`: 搜索关键词
- `limit`: 返回数量限制（可选，默认20）

**示例**: `https://books.jiudianlianxian.com/api/books/search?q=cars&limit=10`

### 3. 阅读历史接口

**URL**: `https://books.jiudianlianxian.com/api/reading-history`

**方法**: POST

**请求体**:
```json
{
  "book_id": "cars_001",
  "read_at": "2024-01-15T10:30:00Z"
}
```

**响应**:
```json
{
  "success": true,
  "message": "阅读历史记录成功",
  "data": {
    "id": "history_001",
    "book_id": "cars_001",
    "read_at": "2024-01-15T10:30:00Z"
  }
}
```

## 🔧 测试步骤

### 1. 测试基础接口

使用浏览器或curl测试：
```bash
curl https://books.jiudianlianxian.com/api/books.json
```

应该返回包含书籍列表的JSON响应。

### 2. 测试文件访问

确保以下文件可以通过HTTP访问：
- 封面图片：`https://books.jiudianlianxian.com/covers/Cars.jpg`
- PDF文件：`https://books.jiudianlianxian.com/pdf/Cars.pdf`
- 音频文件：`https://books.jiudianlianxian.com/audio/Cars.mp3`

### 3. 测试CORS设置

确保API服务器配置了正确的CORS头：
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Accept
```

## 🐛 当前错误分析

### 1. HTTP 404错误
```
记录阅读历史失败: Exception: HTTP错误: 404
```

**原因**: API接口 `/reading-history` 不存在或路径不正确

**解决方案**: 在服务器上实现该接口

### 2. PDF加载失败
```
PDF加载失败: Unable to load asset: "https://books.jiudianlianxian.com/pdf/Cars.pdf"
```

**原因**: PDF文件不存在或无法访问

**解决方案**: 确保PDF文件上传到正确路径并可通过HTTP访问

## 📁 文件结构建议

在您的服务器上建议创建以下目录结构：

```
/api/
  ├── books.json          # 书籍列表API
  ├── books/
  │   └── search          # 搜索API
  └── reading-history     # 阅读历史API

/covers/                  # 封面图片目录
  ├── Cars.jpg
  ├── Animals.jpg
  └── ...

/pdf/                     # PDF文件目录
  ├── Cars.pdf
  ├── Animals.pdf
  └── ...

/audio/                   # 音频文件目录
  ├── Cars.mp3
  ├── Animals.mp3
  └── ...
```

## ✅ 验证清单

- [ ] API接口 `books.json` 返回正确的JSON格式
- [ ] 封面图片可以通过HTTP访问
- [ ] PDF文件可以通过HTTP访问
- [ ] 音频文件可以通过HTTP访问
- [ ] CORS设置正确
- [ ] 搜索接口正常工作
- [ ] 阅读历史接口正常工作

## 🚀 快速测试

您可以先创建一个简单的静态JSON文件来测试：

**创建文件**: `/api/books.json`

```json
{
  "success": true,
  "message": "获取书籍列表成功",
  "data": [
    {
      "id": "cars_001",
      "title": "Cars",
      "author": "Collins Big Cat",
      "level": "红色级别",
      "cover_image": "https://books.jiudianlianxian.com/covers/Cars.jpg",
      "pdf_path": "https://books.jiudianlianxian.com/pdf/Cars.pdf",
      "audio_path": "https://books.jiudianlianxian.com/audio/Cars.mp3",
      "description": "一个关于汽车的精彩故事，适合初学者阅读。",
      "page_count": 16,
      "level_color": "#FF5722"
    }
  ]
}
```

一旦这个文件可以访问，应用就能正常加载数据了！

## 📞 技术支持

如果您在配置API时遇到问题，可以：

1. 检查服务器日志查看具体错误
2. 使用浏览器开发者工具查看网络请求
3. 确认文件权限和路径设置
4. 验证JSON格式是否正确

应用已经完全准备好接收API数据，只需要服务器端配置完成即可！
