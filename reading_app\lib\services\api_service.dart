import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/book.dart';

/// API服务类
class ApiService {
  static const String baseUrl = 'https://books.jiudianlianxian.com/api';
  
  // 单例模式
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  /// 获取所有书籍列表
  Future<List<Book>> getAllBooks() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/books.json'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> booksJson = data['data'];
          return booksJson.map((json) => Book.fromJson(json)).toList();
        } else {
          throw Exception('API返回错误: ${data['message'] ?? '未知错误'}');
        }
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      print('获取书籍列表失败: $e');
      // 返回本地示例数据作为备用
      return Book.getSampleBooks();
    }
  }

  /// 获取推荐书籍
  Future<List<Book>> getRecommendedBooks({int limit = 10}) async {
    try {
      final response = await http.get(
       // Uri.parse('$baseUrl/books/recommended?limit=$limit'),
        Uri.parse('$baseUrl/books.json'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> booksJson = data['data'];
          return booksJson.map((json) => Book.fromJson(json)).toList();
        } else {
          throw Exception('API返回错误: ${data['message'] ?? '未知错误'}');
        }
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      print('获取推荐书籍失败: $e');
      // 返回本地示例数据作为备用
      return Book.getSampleBooks().take(limit).toList();
    }
  }

  /// 获取最近阅读的书籍
  Future<List<Book>> getRecentBooks({int limit = 4}) async {
    try {
      final response = await http.get(
        //Uri.parse('$baseUrl/books/recent?limit=$limit'),
         Uri.parse('$baseUrl/books.json'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> booksJson = data['data'];
          return booksJson.map((json) => Book.fromJson(json)).toList();
        } else {
          throw Exception('API返回错误: ${data['message'] ?? '未知错误'}');
        }
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      print('获取最近阅读书籍失败: $e');
      // 返回本地示例数据作为备用
      return Book.getSampleBooks().take(limit).toList();
    }
  }

  /// 搜索书籍
  Future<List<Book>> searchBooks(String query, {int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/books/search?q=${Uri.encodeComponent(query)}&limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> booksJson = data['data'];
          return booksJson.map((json) => Book.fromJson(json)).toList();
        } else {
          throw Exception('API返回错误: ${data['message'] ?? '未知错误'}');
        }
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      print('搜索书籍失败: $e');
      // 返回空列表
      return [];
    }
  }

  /// 根据级别获取书籍
  Future<List<Book>> getBooksByLevel(String level, {int limit = 20}) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/books/level/${Uri.encodeComponent(level)}?limit=$limit'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> data = json.decode(response.body);
        
        if (data['success'] == true && data['data'] != null) {
          final List<dynamic> booksJson = data['data'];
          return booksJson.map((json) => Book.fromJson(json)).toList();
        } else {
          throw Exception('API返回错误: ${data['message'] ?? '未知错误'}');
        }
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      print('获取级别书籍失败: $e');
      // 返回本地示例数据作为备用
      return Book.getSampleBooks().where((book) => book.level == level).toList();
    }
  }

  /// 记录阅读历史
  Future<bool> recordReadingHistory(String bookId) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/reading-history'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode({
          'book_id': bookId,
          'read_at': DateTime.now().toIso8601String(),
        }),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final Map<String, dynamic> data = json.decode(response.body);
        return data['success'] == true;
      } else {
        throw Exception('HTTP错误: ${response.statusCode}');
      }
    } catch (e) {
      print('记录阅读历史失败: $e');
      return false;
    }
  }
}
