enum AudioPlayerState {
  stopped,
  playing,
  paused,
  loading,
  error,
}

enum RecordingState {
  idle,
  recording,
  paused,
  stopped,
  error,
}

class AudioState {
  final AudioPlayerState playerState;
  final RecordingState recordingState;
  final Duration currentPosition;
  final Duration totalDuration;
  final double volume;
  final double playbackSpeed;
  final String? currentAudioPath;
  final String? currentRecordingPath;
  final String? errorMessage;

  AudioState({
    this.playerState = AudioPlayerState.stopped,
    this.recordingState = RecordingState.idle,
    this.currentPosition = Duration.zero,
    this.totalDuration = Duration.zero,
    this.volume = 1.0,
    this.playbackSpeed = 1.0,
    this.currentAudioPath,
    this.currentRecordingPath,
    this.errorMessage,
  });

  AudioState copyWith({
    AudioPlayerState? playerState,
    RecordingState? recordingState,
    Duration? currentPosition,
    Duration? totalDuration,
    double? volume,
    double? playbackSpeed,
    String? currentAudioPath,
    String? currentRecordingPath,
    String? errorMessage,
  }) {
    return AudioState(
      playerState: playerState ?? this.playerState,
      recordingState: recordingState ?? this.recordingState,
      currentPosition: currentPosition ?? this.currentPosition,
      totalDuration: totalDuration ?? this.totalDuration,
      volume: volume ?? this.volume,
      playbackSpeed: playbackSpeed ?? this.playbackSpeed,
      currentAudioPath: currentAudioPath ?? this.currentAudioPath,
      currentRecordingPath: currentRecordingPath ?? this.currentRecordingPath,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  // 获取播放进度百分比
  double get progressPercentage {
    if (totalDuration.inMilliseconds <= 0) return 0.0;
    return (currentPosition.inMilliseconds / totalDuration.inMilliseconds)
        .clamp(0.0, 1.0);
  }

  // 是否正在播放
  bool get isPlaying => playerState == AudioPlayerState.playing;

  // 是否正在录音
  bool get isRecording => recordingState == RecordingState.recording;

  // 是否有错误
  bool get hasError => 
      playerState == AudioPlayerState.error || 
      recordingState == RecordingState.error;

  @override
  String toString() {
    return 'AudioState(playerState: $playerState, recordingState: $recordingState, position: $currentPosition)';
  }
}
