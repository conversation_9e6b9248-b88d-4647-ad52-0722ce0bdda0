[{"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/default_scroll_handle_right.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-jetified-android-pdf-viewer-3.2.0-beta.3-10:/drawable/default_scroll_handle_right.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/notification_tile_bg.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-core-1.13.1-11:/drawable/notification_tile_bg.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/notification_icon_background.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-core-1.13.1-11:/drawable/notification_icon_background.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/default_scroll_handle_left.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-jetified-android-pdf-viewer-3.2.0-beta.3-10:/drawable/default_scroll_handle_left.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/default_scroll_handle_top.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-jetified-android-pdf-viewer-3.2.0-beta.3-10:/drawable/default_scroll_handle_top.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/notification_bg_low.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-core-1.13.1-11:/drawable/notification_bg_low.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/notification_bg.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-core-1.13.1-11:/drawable/notification_bg.xml"}, {"merged": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-release-26:/drawable/default_scroll_handle_bottom.xml", "source": "io.endigo.plugins.pdfviewflutter.flutter_pdfview-jetified-android-pdf-viewer-3.2.0-beta.3-10:/drawable/default_scroll_handle_bottom.xml"}]