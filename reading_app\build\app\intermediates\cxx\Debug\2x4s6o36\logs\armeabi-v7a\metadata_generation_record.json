[{"level_": 0, "message_": "Start JSON generation. Platform version: 21 min SDK version: armeabi-v7a", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\workspace\\reading\\reading_app\\build\\.cxx\\Debug\\2x4s6o36\\armeabi-v7a\\android_gradle_build.json due to:", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\workspace\\reading\\reading_app\\build\\.cxx\\Debug\\2x4s6o36\\armeabi-v7a'", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\workspace\\reading\\reading_app\\build\\.cxx\\Debug\\2x4s6o36\\armeabi-v7a'", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"E:\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=E:\\\\Android\\\\Sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_ANDROID_NDK=E:\\\\Android\\\\Sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=E:\\\\Android\\\\Sdk\\\\ndk\\\\26.3.11579264\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=E:\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\workspace\\\\reading\\\\reading_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2x4s6o36\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\workspace\\\\reading\\\\reading_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2x4s6o36\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BD:\\\\workspace\\\\reading\\\\reading_app\\\\build\\\\.cxx\\\\Debug\\\\2x4s6o36\\\\armeabi-v7a\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"E:\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\flutter\\\\packages\\\\flutter_tools\\\\gradle\\\\src\\\\main\\\\scripts\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=21\" ^\n  \"-DANDROID_PLATFORM=android-21\" ^\n  \"-DANDROID_ABI=armeabi-v7a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a\" ^\n  \"-DANDROID_NDK=E:\\\\Android\\\\Sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_ANDROID_NDK=E:\\\\Android\\\\Sdk\\\\ndk\\\\26.3.11579264\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=E:\\\\Android\\\\Sdk\\\\ndk\\\\26.3.11579264\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=E:\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\workspace\\\\reading\\\\reading_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2x4s6o36\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\workspace\\\\reading\\\\reading_app\\\\build\\\\app\\\\intermediates\\\\cxx\\\\Debug\\\\2x4s6o36\\\\obj\\\\armeabi-v7a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-BD:\\\\workspace\\\\reading\\\\reading_app\\\\build\\\\.cxx\\\\Debug\\\\2x4s6o36\\\\armeabi-v7a\" ^\n  -GNinja ^\n  -Wno-dev ^\n  --no-warn-unused-cli\n", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "D:\\workspace\\reading\\reading_app\\build\\.cxx\\Debug\\2x4s6o36\\armeabi-v7a\\compile_commands.json.bin existed but not D:\\workspace\\reading\\reading_app\\build\\.cxx\\Debug\\2x4s6o36\\armeabi-v7a\\compile_commands.json", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "profile|armeabi-v7a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]