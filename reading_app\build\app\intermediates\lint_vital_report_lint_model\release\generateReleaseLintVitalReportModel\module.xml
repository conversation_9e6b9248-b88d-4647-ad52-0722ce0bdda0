<lint-module
    format="1"
    dir="D:\workspace\reading\reading_app\android\app"
    name=":app"
    type="APP"
    maven="android:app:unspecified"
    agpVersion="8.7.3"
    buildFolder="D:\workspace\reading\reading_app\build\app"
    bootClassPath="E:\Android\Sdk\platforms\android-35\android.jar;E:\Android\Sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
