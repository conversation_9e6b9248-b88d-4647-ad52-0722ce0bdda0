import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import '../models/book.dart';
import '../models/reading_progress.dart';

class BookService {
  static final BookService _instance = BookService._internal();
  factory BookService() => _instance;
  BookService._internal();

  // 这里可以配置后端API地址
  static const String baseUrl = 'http://localhost:8080/api';
  
  // 本地存储的书籍数据
  List<Book> _cachedBooks = [];
  Map<String, ReadingProgress> _readingProgress = {};

  // 获取所有书籍
  Future<List<Book>> getAllBooks() async {
    try {
      // 首先尝试从网络获取
      final response = await http.get(Uri.parse('$baseUrl/books'));
      if (response.statusCode == 200) {
        final List<dynamic> booksJson = json.decode(response.body);
        _cachedBooks = booksJson.map((json) => Book.fromJson(json)).toList();
        await _saveBooksToLocal(_cachedBooks);
        return _cachedBooks;
      }
    } catch (e) {
      print('网络请求失败，使用本地数据: $e');
    }

    // 如果网络请求失败，使用本地缓存或示例数据
    if (_cachedBooks.isEmpty) {
      _cachedBooks = await _loadBooksFromLocal();
      if (_cachedBooks.isEmpty) {
        _cachedBooks = _getSampleBooks();
      }
    }
    return _cachedBooks;
  }

  // 根据ID获取书籍
  Future<Book?> getBookById(String id) async {
    final books = await getAllBooks();
    try {
      return books.firstWhere((book) => book.id == id);
    } catch (e) {
      return null;
    }
  }

  // 根据等级筛选书籍
  Future<List<Book>> getBooksByLevel(String level) async {
    final books = await getAllBooks();
    return books.where((book) => book.level == level).toList();
  }

  // 搜索书籍
  Future<List<Book>> searchBooks(String query) async {
    final books = await getAllBooks();
    final lowerQuery = query.toLowerCase();
    return books.where((book) => 
      book.title.toLowerCase().contains(lowerQuery) ||
      book.description.toLowerCase().contains(lowerQuery) ||
      book.tags.any((tag) => tag.toLowerCase().contains(lowerQuery))
    ).toList();
  }

  // 获取阅读进度
  Future<ReadingProgress?> getReadingProgress(String bookId) async {
    if (_readingProgress.containsKey(bookId)) {
      return _readingProgress[bookId];
    }

    try {
      final response = await http.get(Uri.parse('$baseUrl/progress/$bookId'));
      if (response.statusCode == 200) {
        final progress = ReadingProgress.fromJson(json.decode(response.body));
        _readingProgress[bookId] = progress;
        return progress;
      }
    } catch (e) {
      print('获取阅读进度失败: $e');
    }

    // 如果没有进度记录，创建新的
    final newProgress = ReadingProgress(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      bookId: bookId,
      currentPage: 1,
      audioPosition: 0.0,
      lastReadAt: DateTime.now(),
      isCompleted: false,
      recordingPaths: [],
    );
    _readingProgress[bookId] = newProgress;
    return newProgress;
  }

  // 更新阅读进度
  Future<void> updateReadingProgress(ReadingProgress progress) async {
    _readingProgress[progress.bookId] = progress;

    try {
      await http.put(
        Uri.parse('$baseUrl/progress/${progress.bookId}'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(progress.toJson()),
      );
    } catch (e) {
      print('更新阅读进度失败: $e');
      // 保存到本地
      await _saveProgressToLocal(progress);
    }
  }

  // 保存书籍到本地
  Future<void> _saveBooksToLocal(List<Book> books) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/books.json');
      final booksJson = books.map((book) => book.toJson()).toList();
      await file.writeAsString(json.encode(booksJson));
    } catch (e) {
      print('保存书籍到本地失败: $e');
    }
  }

  // 从本地加载书籍
  Future<List<Book>> _loadBooksFromLocal() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/books.json');
      if (await file.exists()) {
        final content = await file.readAsString();
        final List<dynamic> booksJson = json.decode(content);
        return booksJson.map((json) => Book.fromJson(json)).toList();
      }
    } catch (e) {
      print('从本地加载书籍失败: $e');
    }
    return [];
  }

  // 保存进度到本地
  Future<void> _saveProgressToLocal(ReadingProgress progress) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/progress_${progress.bookId}.json');
      await file.writeAsString(json.encode(progress.toJson()));
    } catch (e) {
      print('保存进度到本地失败: $e');
    }
  }

  // 获取示例书籍数据
  List<Book> _getSampleBooks() {
    return [
      Book(
        id: '1',
        title: 'Cars',
        description: '一个关于汽车的精彩故事，适合英语学习者阅读和跟读练习。',
        coverImagePath: 'assets/images/Cars.jpg',
        pdfPath: 'assets/books/Cars.pdf',
        audioPath: 'assets/audio/Cars.wma',
        totalPages: 20, // 你可以根据实际PDF页数调整
        level: 'Level 1',
        tags: ['汽车', '冒险', '动画'],
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
    ];
  }

  // 获取所有等级
  Future<List<String>> getAllLevels() async {
    final books = await getAllBooks();
    final levels = books.map((book) => book.level).toSet().toList();
    levels.sort();
    return levels;
  }

  // 清除缓存
  void clearCache() {
    _cachedBooks.clear();
    _readingProgress.clear();
  }
}
