﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-copyfile.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dc9c265f626e8207e5529c5cd5cec5b7\nuget-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\b959c5fb4bb27cd0860ca985810f1f8e\nuget-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\522dc70350a36798a4b5b6a5e19484f9\nuget-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\workspace\reading\reading_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{674D5CAC-29B7-3B99-9D07-A438067F967B}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
