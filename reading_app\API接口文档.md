# 柯林斯大猫分级阅读App API接口文档

## 📋 概述

本文档描述了柯林斯大猫分级阅读App所需的API接口格式和规范。所有API接口都应该返回统一的JSON格式响应。

## 🌐 基础配置

### 基础URL
```
https://your-api-domain.com/api
```

### 请求头
```http
Content-Type: application/json
Accept: application/json
```

### 统一响应格式
所有API接口都应该返回以下格式的JSON响应：

```json
{
  "success": true,
  "message": "操作成功",
  "data": [...] // 具体数据内容
}
```

错误响应格式：
```json
{
  "success": false,
  "message": "错误描述信息",
  "error_code": "ERROR_CODE",
  "data": null
}
```

## 📚 书籍数据模型

### Book对象结构
```json
{
  "id": "cars_001",
  "title": "Cars",
  "author": "Collins Big Cat",
  "level": "红色级别",
  "cover_image": "https://your-cdn.com/covers/cars.jpg",
  "pdf_path": "https://your-cdn.com/books/cars.pdf",
  "audio_path": "https://your-cdn.com/audio/cars.mp3",
  "description": "一个关于汽车的精彩故事，适合初学者阅读。",
  "page_count": 16,
  "level_color": "#FF5722"
}
```

### 字段说明
- `id`: 书籍唯一标识符（字符串）
- `title`: 书籍标题（字符串）
- `author`: 作者（字符串）
- `level`: 阅读级别（字符串，如："红色级别", "黄色级别", "蓝色级别", "绿色级别"）
- `cover_image`: 封面图片URL（字符串）
- `pdf_path`: PDF文件URL（字符串）
- `audio_path`: 音频文件URL（字符串）
- `description`: 书籍描述（字符串）
- `page_count`: 页数（整数）
- `level_color`: 级别颜色代码（字符串，十六进制颜色）

## 🔗 API接口列表

### 1. 获取所有书籍列表

**接口地址：** `GET /books`

**请求参数：** 无

**响应示例：**
```json
{
  "success": true,
  "message": "获取书籍列表成功",
  "data": [
    {
      "id": "cars_001",
      "title": "Cars",
      "author": "Collins Big Cat",
      "level": "红色级别",
      "cover_image": "https://your-cdn.com/covers/cars.jpg",
      "pdf_path": "https://your-cdn.com/books/cars.pdf",
      "audio_path": "https://your-cdn.com/audio/cars.mp3",
      "description": "一个关于汽车的精彩故事",
      "page_count": 16,
      "level_color": "#FF5722"
    }
  ]
}
```

### 2. 获取推荐书籍

**接口地址：** `GET /books/recommended`

**请求参数：**
- `limit` (可选): 返回数量限制，默认10

**请求示例：**
```
GET /books/recommended?limit=10
```

**响应格式：** 与获取所有书籍列表相同

### 3. 获取最近阅读书籍

**接口地址：** `GET /books/recent`

**请求参数：**
- `limit` (可选): 返回数量限制，默认4

**请求示例：**
```
GET /books/recent?limit=4
```

**响应格式：** 与获取所有书籍列表相同

### 4. 搜索书籍

**接口地址：** `GET /books/search`

**请求参数：**
- `q` (必需): 搜索关键词
- `limit` (可选): 返回数量限制，默认20

**请求示例：**
```
GET /books/search?q=cars&limit=20
```

**响应格式：** 与获取所有书籍列表相同

### 5. 根据级别获取书籍

**接口地址：** `GET /books/level/{level}`

**请求参数：**
- `level` (路径参数): 阅读级别（如：红色级别）
- `limit` (可选): 返回数量限制，默认20

**请求示例：**
```
GET /books/level/红色级别?limit=20
```

**响应格式：** 与获取所有书籍列表相同

### 6. 记录阅读历史

**接口地址：** `POST /reading-history`

**请求体：**
```json
{
  "book_id": "cars_001",
  "read_at": "2024-01-15T10:30:00Z"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "阅读历史记录成功",
  "data": {
    "id": "history_001",
    "book_id": "cars_001",
    "read_at": "2024-01-15T10:30:00Z"
  }
}
```

## 🔧 实现建议

### 1. 文件存储
- **封面图片**: 建议使用CDN存储，支持HTTPS访问
- **PDF文件**: 建议使用CDN存储，支持断点续传
- **音频文件**: 建议使用CDN存储，支持流式播放

### 2. 缓存策略
- 书籍列表数据建议缓存30分钟
- 搜索结果建议缓存10分钟
- 推荐书籍建议缓存1小时

### 3. 分页支持
对于大量数据的接口，建议支持分页：
```
GET /books?page=1&limit=20
```

### 4. 错误处理
常见HTTP状态码：
- `200`: 成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

### 5. 安全考虑
- 所有文件URL应该支持HTTPS
- 考虑添加API访问频率限制
- 敏感操作可以添加身份验证

## 📱 客户端集成

App已经集成了完整的API调用逻辑：

1. **ApiService类**: 封装了所有API调用方法
2. **错误处理**: 网络错误时自动回退到本地数据
3. **加载状态**: 提供加载指示器和错误提示
4. **搜索功能**: 支持实时搜索和防抖处理
5. **阅读历史**: 自动记录用户阅读行为

## 🚀 部署建议

1. **API服务器**: 建议使用Node.js、Python Django/Flask或PHP Laravel等
2. **数据库**: 建议使用MySQL或PostgreSQL存储书籍信息
3. **文件存储**: 建议使用阿里云OSS、腾讯云COS或AWS S3等CDN服务
4. **监控**: 建议添加API调用监控和错误日志记录

通过实现以上API接口，App将能够动态加载书籍内容，提供更好的用户体验。
