^D:\WORKSPACE\READING\READING_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\522DC70350A36798A4B5B6A5E19484F9\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-subbuild -BD:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-subbuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/workspace/reading/reading_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
