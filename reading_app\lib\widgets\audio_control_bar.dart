import 'package:flutter/material.dart';
import '../services/audio_service.dart';
import '../constants/app_constants.dart';

/// 音频控制栏组件
class AudioControlBar extends StatefulWidget {
  final String audioPath;
  final VoidCallback? onPlayStateChanged;

  const AudioControlBar({
    super.key,
    required this.audioPath,
    this.onPlayStateChanged,
  });

  @override
  State<AudioControlBar> createState() => _AudioControlBarState();
}

class _AudioControlBarState extends State<AudioControlBar> {
  final AudioService _audioService = AudioService();
  late Stream<void> _audioStateStream;

  @override
  void initState() {
    super.initState();
    _audioService.initialize();
    // 创建一个简单的状态更新流
    _audioStateStream = Stream.periodic(
      const Duration(milliseconds: 100),
      (_) {},
    );
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.primaryColor,
            AppConstants.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
      ),
      child: StreamBuilder<void>(
        stream: _audioStateStream,
        builder: (context, snapshot) {
          return Row(
            children: [
              // 播放/暂停按钮
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: _togglePlayPause,
                  icon: Icon(
                    _audioService.isPlaying ? Icons.pause : Icons.play_arrow,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ),

              const SizedBox(width: AppConstants.paddingMedium),
              
              // 进度条
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SliderTheme(
                      data: SliderTheme.of(context).copyWith(
                        activeTrackColor: Colors.white,
                        inactiveTrackColor: Colors.white.withValues(alpha: 0.3),
                        thumbColor: Colors.white,
                        overlayColor: Colors.white.withValues(alpha: 0.2),
                        trackHeight: 4,
                        thumbShape: const RoundSliderThumbShape(
                          enabledThumbRadius: 8,
                        ),
                      ),
                      child: Slider(
                        value: _audioService.duration.inMilliseconds > 0
                            ? _audioService.position.inMilliseconds /
                                _audioService.duration.inMilliseconds
                            : 0.0,
                        onChanged: (value) {
                          final position = Duration(
                            milliseconds: (value * _audioService.duration.inMilliseconds).round(),
                          );
                          _audioService.seek(position);
                        },
                      ),
                    ),
                    
                    // 时间显示
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AudioService.formatDuration(_audioService.position),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          AudioService.formatDuration(_audioService.duration),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              const SizedBox(width: AppConstants.paddingMedium),

              // 停止按钮
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: () {
                    _audioService.stop();
                    widget.onPlayStateChanged?.call();
                  },
                  icon: const Icon(
                    Icons.stop,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _togglePlayPause() {
    if (_audioService.isPlaying) {
      _audioService.pause();
    } else if (_audioService.isPaused) {
      _audioService.resume();
    } else {
      _audioService.play(widget.audioPath);

      // 检查播放状态，如果是错误状态则显示提示
      Future.delayed(const Duration(milliseconds: 500), () {
        if (_audioService.state == AudioState.error) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  widget.audioPath.toLowerCase().endsWith('.wma')
                      ? '不支持WMA格式音频，请联系管理员转换为MP3格式'
                      : '音频播放失败，请检查网络连接或文件格式',
                ),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      });
    }
    widget.onPlayStateChanged?.call();
  }
}
