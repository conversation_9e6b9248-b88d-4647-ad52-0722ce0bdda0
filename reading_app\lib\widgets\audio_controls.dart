import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/audio_state.dart';

class AudioControls extends StatelessWidget {
  const AudioControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        final audioState = provider.audioState;
        
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            children: [
              // 音频进度条
              _buildProgressBar(context, provider, audioState),
              
              const SizedBox(height: 12),
              
              // 控制按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // 播放速度
                  _buildSpeedButton(context, provider, audioState),
                  
                  // 后退15秒
                  _buildSeekButton(
                    context,
                    provider,
                    Icons.replay,
                    () => _seekRelative(provider, -15),
                  ),
                  
                  // 播放/暂停按钮
                  _buildPlayPauseButton(context, provider, audioState),
                  
                  // 前进15秒
                  _buildSeekButton(
                    context,
                    provider,
                    Icons.fast_forward,
                    () => _seekRelative(provider, 15),
                  ),
                  
                  // 音量控制
                  _buildVolumeButton(context, provider, audioState),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressBar(BuildContext context, AppProvider provider, AudioState audioState) {
    return Column(
      children: [
        Row(
          children: [
            Text(
              _formatDuration(audioState.currentPosition),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
            Expanded(
              child: Slider(
                value: audioState.progressPercentage,
                onChanged: (value) {
                  final newPosition = Duration(
                    milliseconds: (value * audioState.totalDuration.inMilliseconds).round(),
                  );
                  provider.seekAudio(newPosition);
                },
                activeColor: const Color(0xFF4A90E2),
                inactiveColor: Colors.white30,
                thumbColor: Colors.white,
              ),
            ),
            Text(
              _formatDuration(audioState.totalDuration),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPlayPauseButton(BuildContext context, AppProvider provider, AudioState audioState) {
    IconData icon;
    VoidCallback? onPressed;

    switch (audioState.playerState) {
      case AudioPlayerState.playing:
        icon = Icons.pause;
        onPressed = () => provider.pauseAudio();
        break;
      case AudioPlayerState.paused:
        icon = Icons.play_arrow;
        onPressed = () => provider.resumeAudio();
        break;
      case AudioPlayerState.stopped:
        icon = Icons.play_arrow;
        onPressed = () => provider.playAudio();
        break;
      case AudioPlayerState.loading:
        icon = Icons.hourglass_empty;
        onPressed = null;
        break;
      case AudioPlayerState.error:
        icon = Icons.error;
        onPressed = () => provider.playAudio();
        break;
    }

    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF4A90E2),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        icon: Icon(icon),
        iconSize: 32,
        color: Colors.white,
        onPressed: onPressed,
      ),
    );
  }

  Widget _buildSeekButton(BuildContext context, AppProvider provider, IconData icon, VoidCallback onPressed) {
    return IconButton(
      icon: Icon(icon),
      iconSize: 28,
      color: Colors.white,
      onPressed: onPressed,
    );
  }

  Widget _buildSpeedButton(BuildContext context, AppProvider provider, AudioState audioState) {
    return GestureDetector(
      onTap: () => _showSpeedDialog(context, provider, audioState),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white70),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '${audioState.playbackSpeed}x',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildVolumeButton(BuildContext context, AppProvider provider, AudioState audioState) {
    return GestureDetector(
      onTap: () => _showVolumeDialog(context, provider, audioState),
      child: Icon(
        audioState.volume > 0.5
            ? Icons.volume_up
            : audioState.volume > 0
                ? Icons.volume_down
                : Icons.volume_off,
        color: Colors.white,
        size: 24,
      ),
    );
  }

  void _seekRelative(AppProvider provider, int seconds) {
    final currentPosition = provider.audioState.currentPosition;
    final newPosition = currentPosition + Duration(seconds: seconds);
    final clampedPosition = Duration(
      milliseconds: newPosition.inMilliseconds.clamp(
        0,
        provider.audioState.totalDuration.inMilliseconds,
      ),
    );
    provider.seekAudio(clampedPosition);
  }

  void _showSpeedDialog(BuildContext context, AppProvider provider, AudioState audioState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('播放速度'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0].map((speed) {
            return ListTile(
              title: Text('${speed}x'),
              leading: Radio<double>(
                value: speed,
                groupValue: audioState.playbackSpeed,
                onChanged: (value) {
                  if (value != null) {
                    provider.setPlaybackSpeed(value);
                    Navigator.pop(context);
                  }
                },
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  void _showVolumeDialog(BuildContext context, AppProvider provider, AudioState audioState) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('音量'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Slider(
              value: audioState.volume,
              onChanged: (value) {
                provider.setVolume(value);
              },
              min: 0.0,
              max: 1.0,
              divisions: 10,
              label: '${(audioState.volume * 100).round()}%',
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
