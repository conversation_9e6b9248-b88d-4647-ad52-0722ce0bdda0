class ReadingProgress {
  final String id;
  final String bookId;
  final int currentPage;
  final double audioPosition; // 音频播放位置（秒）
  final DateTime lastReadAt;
  final bool isCompleted;
  final List<String> recordingPaths; // 用户录音文件路径列表

  ReadingProgress({
    required this.id,
    required this.bookId,
    required this.currentPage,
    required this.audioPosition,
    required this.lastReadAt,
    required this.isCompleted,
    required this.recordingPaths,
  });

  factory ReadingProgress.fromJson(Map<String, dynamic> json) {
    return ReadingProgress(
      id: json['id'] as String,
      bookId: json['bookId'] as String,
      currentPage: json['currentPage'] as int,
      audioPosition: (json['audioPosition'] as num).toDouble(),
      lastReadAt: DateTime.parse(json['lastReadAt'] as String),
      isCompleted: json['isCompleted'] as bool,
      recordingPaths: List<String>.from(json['recordingPaths'] as List),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bookId': bookId,
      'currentPage': currentPage,
      'audioPosition': audioPosition,
      'lastReadAt': lastReadAt.toIso8601String(),
      'isCompleted': isCompleted,
      'recordingPaths': recordingPaths,
    };
  }

  ReadingProgress copyWith({
    String? id,
    String? bookId,
    int? currentPage,
    double? audioPosition,
    DateTime? lastReadAt,
    bool? isCompleted,
    List<String>? recordingPaths,
  }) {
    return ReadingProgress(
      id: id ?? this.id,
      bookId: bookId ?? this.bookId,
      currentPage: currentPage ?? this.currentPage,
      audioPosition: audioPosition ?? this.audioPosition,
      lastReadAt: lastReadAt ?? this.lastReadAt,
      isCompleted: isCompleted ?? this.isCompleted,
      recordingPaths: recordingPaths ?? this.recordingPaths,
    );
  }

  // 计算阅读进度百分比
  double getProgressPercentage(int totalPages) {
    if (totalPages <= 0) return 0.0;
    return (currentPage / totalPages).clamp(0.0, 1.0);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReadingProgress && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ReadingProgress(id: $id, bookId: $bookId, currentPage: $currentPage)';
  }
}
