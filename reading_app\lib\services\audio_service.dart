import 'package:audioplayers/audioplayers.dart';

/// 音频播放状态
enum AudioState {
  stopped,
  playing,
  paused,
  loading,
  error,
}

/// 音频服务类 - 处理音频播放相关操作
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  AudioState _state = AudioState.stopped;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;

  // Getters
  AudioState get state => _state;
  Duration get duration => _duration;
  Duration get position => _position;
  bool get isPlaying => _state == AudioState.playing;
  bool get isPaused => _state == AudioState.paused;
  bool get isLoading => _state == AudioState.loading;

  /// 初始化音频播放器
  void initialize() {
    _audioPlayer.onDurationChanged.listen((duration) {
      _duration = duration;
    });

    _audioPlayer.onPositionChanged.listen((position) {
      _position = position;
    });

    _audioPlayer.onPlayerStateChanged.listen((state) {
      switch (state) {
        case PlayerState.playing:
          _state = AudioState.playing;
          break;
        case PlayerState.paused:
          _state = AudioState.paused;
          break;
        case PlayerState.stopped:
          _state = AudioState.stopped;
          break;
        case PlayerState.completed:
          _state = AudioState.stopped;
          _position = Duration.zero;
          break;
        case PlayerState.disposed:
          _state = AudioState.stopped;
          break;
      }
    });
  }

  /// 播放音频文件
  Future<void> play(String assetPath) async {
    try {
      _state = AudioState.loading;
      await _audioPlayer.play(AssetSource(assetPath));
    } catch (e) {
      _state = AudioState.error;
      print('音频播放失败: $e');
    }
  }

  /// 暂停播放
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  /// 继续播放
  Future<void> resume() async {
    await _audioPlayer.resume();
  }

  /// 停止播放
  Future<void> stop() async {
    await _audioPlayer.stop();
  }

  /// 跳转到指定位置
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// 设置音量 (0.0 - 1.0)
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume);
  }

  /// 释放资源
  void dispose() {
    try {
      _audioPlayer.stop();
      _audioPlayer.dispose();
    } catch (e) {
      // 忽略销毁时的错误
      print('Audio service dispose error: $e');
    }
  }

  /// 格式化时间显示
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
