import 'package:audioplayers/audioplayers.dart';

/// 音频播放状态
enum AudioState {
  stopped,
  playing,
  paused,
  loading,
  error,
}

/// 音频服务类 - 处理音频播放相关操作
class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  AudioState _state = AudioState.stopped;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;

  // Getters
  AudioState get state => _state;
  Duration get duration => _duration;
  Duration get position => _position;
  bool get isPlaying => _state == AudioState.playing;
  bool get isPaused => _state == AudioState.paused;
  bool get isLoading => _state == AudioState.loading;

  /// 初始化音频播放器
  void initialize() {
    _audioPlayer.onDurationChanged.listen((duration) {
      _duration = duration;
    });

    _audioPlayer.onPositionChanged.listen((position) {
      _position = position;
    });

    _audioPlayer.onPlayerStateChanged.listen((state) {
      switch (state) {
        case PlayerState.playing:
          _state = AudioState.playing;
          break;
        case PlayerState.paused:
          _state = AudioState.paused;
          break;
        case PlayerState.stopped:
          _state = AudioState.stopped;
          break;
        case PlayerState.completed:
          _state = AudioState.stopped;
          _position = Duration.zero;
          break;
        case PlayerState.disposed:
          _state = AudioState.stopped;
          break;
      }
    });
  }

  /// 播放音频文件（支持本地资源和网络URL）
  Future<void> play(String audioPath) async {
    try {
      // 先停止当前播放的音频
      await stop();

      _state = AudioState.loading;

      // 检查音频格式
      if (!_isSupportedAudioFormat(audioPath)) {
        _state = AudioState.error;
        print('不支持的音频格式: $audioPath');
        print('支持的格式: MP3, AAC, OGG, WAV, M4A');
        return;
      }

      // 判断是否为网络URL
      if (audioPath.startsWith('http://') || audioPath.startsWith('https://')) {
        print('播放网络音频: $audioPath');
        await _audioPlayer.play(UrlSource(audioPath));
      } else {
        print('播放本地音频: $audioPath');
        // 移除 assets/ 前缀（如果存在）
        final cleanPath = audioPath.startsWith('assets/')
            ? audioPath.substring(7)
            : audioPath;
        await _audioPlayer.play(AssetSource(cleanPath));
      }
    } catch (e) {
      _state = AudioState.error;
      print('音频播放失败: $e');
    }
  }

  /// 检查是否为支持的音频格式
  bool _isSupportedAudioFormat(String audioPath) {
    final supportedFormats = ['.mp3', '.aac', '.ogg', '.wav', '.m4a'];
    final lowerPath = audioPath.toLowerCase();
    return supportedFormats.any((format) => lowerPath.endsWith(format));
  }

  /// 暂停播放
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  /// 继续播放
  Future<void> resume() async {
    await _audioPlayer.resume();
  }

  /// 停止播放
  Future<void> stop() async {
    try {
      await _audioPlayer.stop();
      _position = Duration.zero;
      _duration = Duration.zero;
      print('音频已停止');
    } catch (e) {
      print('停止音频失败: $e');
    }
  }

  /// 跳转到指定位置
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  /// 设置音量 (0.0 - 1.0)
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume);
  }

  /// 释放资源
  void dispose() {
    try {
      _audioPlayer.stop();
      _audioPlayer.dispose();
    } catch (e) {
      // 忽略销毁时的错误
      print('Audio service dispose error: $e');
    }
  }

  /// 格式化时间显示
  static String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
