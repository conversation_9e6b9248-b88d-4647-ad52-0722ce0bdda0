import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
// import 'package:record/record.dart';  // 暂时移除录音功能
import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart';  // 暂时移除权限处理
import '../models/audio_state.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  // final AudioRecorder _audioRecorder = AudioRecorder();  // 暂时移除录音功能
  
  StreamController<AudioState> _stateController = StreamController<AudioState>.broadcast();
  AudioState _currentState = AudioState();

  Stream<AudioState> get stateStream => _stateController.stream;
  AudioState get currentState => _currentState;

  Timer? _positionTimer;

  Future<void> initialize() async {
    // 监听播放器状态变化
    _audioPlayer.onPlayerStateChanged.listen((PlayerState state) {
      AudioPlayerState newState;
      switch (state) {
        case PlayerState.stopped:
          newState = AudioPlayerState.stopped;
          break;
        case PlayerState.playing:
          newState = AudioPlayerState.playing;
          break;
        case PlayerState.paused:
          newState = AudioPlayerState.paused;
          break;
        case PlayerState.completed:
          newState = AudioPlayerState.stopped;
          break;
        case PlayerState.disposed:
          newState = AudioPlayerState.stopped;
          break;
      }
      _updateState(_currentState.copyWith(playerState: newState));
    });

    // 监听播放位置变化
    _audioPlayer.onPositionChanged.listen((Duration position) {
      _updateState(_currentState.copyWith(currentPosition: position));
    });

    // 监听音频时长变化
    _audioPlayer.onDurationChanged.listen((Duration duration) {
      _updateState(_currentState.copyWith(totalDuration: duration));
    });
  }

  Future<bool> requestPermissions() async {
    // 权限请求暂时禁用
    return false;
  }

  Future<void> playAudio(String audioPath) async {
    try {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.loading,
        currentAudioPath: audioPath,
      ));

      print('尝试播放音频: $audioPath'); // 调试信息

      if (audioPath.startsWith('http')) {
        await _audioPlayer.play(UrlSource(audioPath));
      } else if (audioPath.startsWith('assets/')) {
        final assetPath = audioPath.replaceFirst('assets/', '');
        print('播放资源文件: $assetPath'); // 调试信息
        await _audioPlayer.play(AssetSource(assetPath));
      } else {
        await _audioPlayer.play(DeviceFileSource(audioPath));
      }
    } catch (e) {
      print('播放音频失败: $e'); // 调试信息
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> pauseAudio() async {
    try {
      await _audioPlayer.pause();
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> resumeAudio() async {
    try {
      await _audioPlayer.resume();
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> stopAudio() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> seekTo(Duration position) async {
    try {
      await _audioPlayer.seek(position);
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> setVolume(double volume) async {
    try {
      await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
      _updateState(_currentState.copyWith(volume: volume));
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<void> setPlaybackSpeed(double speed) async {
    try {
      await _audioPlayer.setPlaybackRate(speed.clamp(0.5, 2.0));
      _updateState(_currentState.copyWith(playbackSpeed: speed));
    } catch (e) {
      _updateState(_currentState.copyWith(
        playerState: AudioPlayerState.error,
        errorMessage: e.toString(),
      ));
    }
  }

  Future<String?> startRecording() async {
    // 录音功能暂时禁用
    _updateState(_currentState.copyWith(
      recordingState: RecordingState.error,
      errorMessage: '录音功能暂时不可用',
    ));
    return null;
  }

  Future<String?> stopRecording() async {
    // 录音功能暂时禁用
    _updateState(_currentState.copyWith(
      recordingState: RecordingState.stopped,
    ));
    return null;
  }

  void _updateState(AudioState newState) {
    _currentState = newState;
    _stateController.add(_currentState);
  }

  void dispose() {
    _positionTimer?.cancel();
    _audioPlayer.dispose();
    _audioRecorder.dispose();
    _stateController.close();
  }
}
