{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "E:/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "E:/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "E:/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "E:/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-62345cfd9265b1b94c12.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-33b8f3bd7703f9e398f1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-73a6a819d5fed548ee07.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-33b8f3bd7703f9e398f1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-73a6a819d5fed548ee07.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-62345cfd9265b1b94c12.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}