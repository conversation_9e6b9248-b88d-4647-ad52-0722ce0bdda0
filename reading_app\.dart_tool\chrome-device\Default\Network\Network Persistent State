{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "broken_count": 1, "broken_until": "1751521672", "host": "copilot.microsoft.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABIAAABodHRwczovL29mZmljZS5jb20AAA==", false, 0], "broken_count": 1, "broken_until": "1751521673", "host": "substrate.office.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "broken_count": 4, "broken_until": "1751521678", "host": "fonts.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABQAAABodHRwczovL3Vuc3BsYXNoLmNvbQ==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "edge.fullstory.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "broken_count": 1, "broken_until": "**********", "host": "www.gstatic.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "accounts.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "www.google-analytics.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "connect.facebook.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "broken_count": 1, "broken_until": "**********", "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "broken_count": 1, "broken_until": "**********", "host": "www.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://prod.rewardsplatform.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396088572277141", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://copilot.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396088573366588", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2JpbmcuY29t", false, 0], "server": "https://www.bing.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586973512707", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL29mZmljZS5jb20AAA==", false, 0], "server": "https://substrate.office.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396081373539818", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://cdnjs.cloudflare.com", "supports_spdy": true}, {"anonymization": ["LAAAACYAAABodHRwczovL2VkZ2Vhc3NldHNlcnZpY2UuYXp1cmVlZGdlLm5ldAAA", false, 0], "server": "https://edgeassetservice.azureedge.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586974914133", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586982907358", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586989127003", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://clients2.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3Vuc3BsYXNoLmNvbQ==", false, 0], "server": "https://images.unsplash.com", "supports_spdy": true}, {"anonymization": ["GAAAABEAAABodHRwczovL2JkaW1nLmNvbQAAAA==", false, 0], "server": "https://wkretype.bdimg.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3Vuc3BsYXNoLmNvbQ==", false, 0], "server": "https://unsplash-assets.imgix.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586990126888", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3Vuc3BsYXNoLmNvbQ==", false, 0], "server": "https://plus.unsplash.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586991530679", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL3Vuc3BsYXNoLmNvbQ==", false, 0], "server": "https://edge.fullstory.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL3Vuc3BsYXNoLmNvbQ==", false, 0], "server": "https://unsplash.com", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586997366471", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://www.recaptcha.net", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL2JpbmdhcGlzLmNvbQ==", false, 0], "server": "https://www.bingapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398586998564600", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://geolocation.onetrust.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587000069945", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "server": "https://www.recaptcha.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587001959082", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://cdn.cookielaw.org", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587006019000", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://googleads.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396081406065776", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://connect.facebook.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587006172671", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "server": "https://td.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587006191663", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587006349658", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398587006431235", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://d35aaqx5ub95lt.cloudfront.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396081407120208", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://www.facebook.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://excess.duolingo.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL2R1b2xpbmdvLmNuAA==", false, 0], "server": "https://privacyportal-eu.onetrust.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2RvdXlpbi5jb20AAA==", false, 0], "server": "https://www.douyin.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "3G"}}}