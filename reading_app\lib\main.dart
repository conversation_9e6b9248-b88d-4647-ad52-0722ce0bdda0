import 'package:flutter/material.dart';
import 'screens/splash_screen.dart';

void main() {
  runApp(const CollinsBigCatApp());
}

class CollinsBigCatApp extends StatelessWidget {
  const CollinsBigCatApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '柯林斯大猫分级阅读',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        primaryColor: const Color(0xFFFF6B35), // 柯林斯大猫橙色主题
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFFF6B35),
          brightness: Brightness.light,
        ),
        useMaterial3: true,
        fontFamily: 'PingFang SC', // 中文字体
      ),
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}