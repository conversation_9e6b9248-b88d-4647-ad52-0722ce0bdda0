import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import '../services/pdf_service.dart';
import '../widgets/audio_control_bar.dart';
import '../constants/app_constants.dart';
import '../models/book.dart';

/// 阅读页
class ReadingPage extends StatefulWidget {
  final Book? book;

  const ReadingPage({super.key, this.book});

  @override
  State<ReadingPage> createState() => _ReadingPageState();
}

class _ReadingPageState extends State<ReadingPage> {
  final PDFService _pdfService = PDFService();
  bool _isLoading = false;
  String? _pdfPath;
  late Book _currentBook;
  int _currentPage = 0;
  int _totalPages = 0;

  @override
  void initState() {
    super.initState();
    _currentBook = widget.book ?? Book.getSampleBooks().first;
    _loadPDF();
  }

  Future<void> _loadPDF() async {
    setState(() => _isLoading = true);

    final pdfPath = await _pdfService.loadPDF(_currentBook.pdfPath);

    if (mounted) {
      setState(() {
        _pdfPath = pdfPath;
        _isLoading = false;
      });

      if (pdfPath == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('PDF加载失败，请检查网络连接')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        backgroundColor: AppConstants.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: Text(
          _currentBook.title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppConstants.fontSizeXLarge,
          ),
        ),
        actions: [
          // 页数显示
          if (_totalPages > 0)
            Container(
              margin: const EdgeInsets.only(right: AppConstants.paddingMedium),
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingSmall,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(AppConstants.radiusSmall),
              ),
              child: Text(
                '${_currentPage + 1}/$_totalPages',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: AppConstants.fontSizeMedium,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const CircularProgressIndicator(
                    color: AppConstants.primaryColor,
                    strokeWidth: 3,
                  ),
                  const SizedBox(height: AppConstants.paddingMedium),
                  Text(
                    _currentBook.pdfPath.startsWith('http')
                        ? '正在下载PDF...'
                        : '正在加载书籍...',
                    style: TextStyle(
                      color: AppConstants.textSecondaryColor,
                      fontSize: AppConstants.fontSizeMedium,
                    ),
                  ),
                  if (_currentBook.pdfPath.startsWith('http'))
                    Padding(
                      padding: const EdgeInsets.only(top: AppConstants.paddingSmall),
                      child: Text(
                        '首次下载可能需要一些时间',
                        style: TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeSmall,
                        ),
                      ),
                    ),
                ],
              ),
            )
          : _pdfPath == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppConstants.textSecondaryColor,
                      ),
                      const SizedBox(height: AppConstants.paddingMedium),
                      Text(
                        'PDF加载失败',
                        style: TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        '请检查网络连接或重试',
                        style: TextStyle(
                          color: AppConstants.textSecondaryColor,
                          fontSize: AppConstants.fontSizeMedium,
                        ),
                      ),
                    ],
                  ),
                )
              : Column(
                  children: [
                    // PDF查看器容器
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.all(AppConstants.paddingMedium),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                          child: PDFView(
                            filePath: _pdfPath!,
                            enableSwipe: true,
                            swipeHorizontal: true,
                            autoSpacing: false,
                            pageFling: false,
                            onRender: (pages) {
                              setState(() {
                                _totalPages = pages ?? 0;
                              });
                            },
                            onViewCreated: (PDFViewController controller) {
                              // PDF控制器创建完成
                            },
                            onPageChanged: (page, total) {
                              setState(() {
                                _currentPage = page ?? 0;
                              });
                            },
                            onError: (error) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('PDF显示错误: $error'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),

                    // 功能按钮区域
                    Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingMedium,
                        vertical: AppConstants.paddingSmall,
                      ),
                      padding: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          // 功能标题
                          Row(
                            children: [
                              Icon(
                                Icons.auto_awesome,
                                color: AppConstants.primaryColor,
                                size: 20,
                              ),
                              const SizedBox(width: AppConstants.paddingSmall),
                              Text(
                                '学习工具',
                                style: TextStyle(
                                  color: AppConstants.textPrimaryColor,
                                  fontSize: AppConstants.fontSizeMedium,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: AppConstants.paddingMedium),
                          // 功能按钮
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildFeatureButton(
                                icon: Icons.record_voice_over,
                                label: '跟读',
                                onTap: () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: const Text('跟读功能开发中...'),
                                      backgroundColor: AppConstants.primaryColor,
                                    ),
                                  );
                                },
                              ),
                              _buildFeatureButton(
                                icon: Icons.touch_app,
                                label: '点读',
                                onTap: () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: const Text('点读功能开发中...'),
                                      backgroundColor: AppConstants.primaryColor,
                                    ),
                                  );
                                },
                              ),
                              _buildFeatureButton(
                                icon: Icons.book,
                                label: '生词本',
                                onTap: () {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: const Text('生词本功能开发中...'),
                                      backgroundColor: AppConstants.primaryColor,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // 音频控制栏
                    Container(
                      margin: const EdgeInsets.all(AppConstants.paddingMedium),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: AudioControlBar(
                        audioPath: _currentBook.audioPath.replaceFirst('assets/', ''),
                        onPlayStateChanged: () {
                          // 音频状态改变回调
                        },
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildFeatureButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          padding: const EdgeInsets.symmetric(
            horizontal: AppConstants.paddingSmall,
            vertical: AppConstants.paddingMedium,
          ),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppConstants.primaryColor.withValues(alpha: 0.1),
                AppConstants.primaryColor.withValues(alpha: 0.05),
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
            borderRadius: BorderRadius.circular(AppConstants.radiusMedium),
            border: Border.all(
              color: AppConstants.primaryColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppConstants.primaryColor.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  color: AppConstants.primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                label,
                style: TextStyle(
                  color: AppConstants.primaryColor,
                  fontSize: AppConstants.fontSizeSmall,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
