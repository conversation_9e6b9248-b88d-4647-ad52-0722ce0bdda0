import 'package:flutter/material.dart';

/// 书籍封面图片组件
/// 自动判断是网络图片还是本地资源图片
class BookCoverImage extends StatelessWidget {
  final String imagePath;
  final BoxFit fit;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const BookCoverImage({
    super.key,
    required this.imagePath,
    this.fit = BoxFit.cover,
    this.width,
    this.height,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // 判断是否为网络图片
    final bool isNetworkImage = imagePath.startsWith('http://') || imagePath.startsWith('https://');
    
    Widget imageWidget;
    
    if (isNetworkImage) {
      // 网络图片
      imageWidget = Image.network(
        imagePath,
        fit: fit,
        width: width,
        height: height,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            child: Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                    : null,
                strokeWidth: 2,
              ),
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          // 网络图片加载失败时显示默认图片
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            child: const Icon(
              Icons.book,
              color: Colors.grey,
              size: 48,
            ),
          );
        },
      );
    } else {
      // 本地资源图片
      imageWidget = Image.asset(
        imagePath,
        fit: fit,
        width: width,
        height: height,
        errorBuilder: (context, error, stackTrace) {
          // 本地图片加载失败时显示默认图片
          return Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: borderRadius,
            ),
            child: const Icon(
              Icons.book,
              color: Colors.grey,
              size: 48,
            ),
          );
        },
      );
    }

    // 如果指定了圆角，包装在ClipRRect中
    if (borderRadius != null) {
      return ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}

/// 书籍封面装饰容器
/// 用于DecorationImage的场景
class BookCoverDecorationImage extends DecorationImage {
  BookCoverDecorationImage({
    required String imagePath,
    BoxFit fit = BoxFit.cover,
  }) : super(
          image: _getImageProvider(imagePath),
          fit: fit,
          onError: (exception, stackTrace) {
            // 图片加载错误时的处理
            debugPrint('图片加载失败: $imagePath, 错误: $exception');
          },
        );

  static ImageProvider _getImageProvider(String imagePath) {
    // 判断是否为网络图片
    if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
      return NetworkImage(imagePath);
    } else {
      return AssetImage(imagePath);
    }
  }
}
