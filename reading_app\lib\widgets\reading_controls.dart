import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/app_provider.dart';
import '../models/audio_state.dart';

class ReadingControls extends StatelessWidget {
  const ReadingControls({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AppProvider>(
      builder: (context, provider, child) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              // 页面导航
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 上一页按钮
                  _buildNavigationButton(
                    context,
                    Icons.navigate_before,
                    '上一页',
                    provider.currentPage > 1,
                    () => provider.previousPage(),
                  ),
                  
                  // 页面指示器
                  _buildPageIndicator(context, provider),
                  
                  // 下一页按钮
                  _buildNavigationButton(
                    context,
                    Icons.navigate_next,
                    '下一页',
                    provider.currentPage < provider.totalPages,
                    () => provider.nextPage(),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 跟读控制
              _buildRecordingControls(context, provider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavigationButton(
    BuildContext context,
    IconData icon,
    String label,
    bool enabled,
    VoidCallback onPressed,
  ) {
    return Opacity(
      opacity: enabled ? 1.0 : 0.5,
      child: ElevatedButton.icon(
        onPressed: enabled ? onPressed : null,
        icon: Icon(icon, size: 18),
        label: Text(label),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.white.withOpacity(0.9),
          foregroundColor: const Color(0xFF4A90E2),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      ),
    );
  }

  Widget _buildPageIndicator(BuildContext context, AppProvider provider) {
    return GestureDetector(
      onTap: () => _showPageSelector(context, provider),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            Text(
              '${provider.currentPage} / ${provider.totalPages}',
              style: const TextStyle(
                color: Color(0xFF4A90E2),
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: 80,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: provider.totalPages > 0 
                    ? provider.currentPage / provider.totalPages 
                    : 0.0,
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFF4A90E2),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordingControls(BuildContext context, AppProvider provider) {
    final audioState = provider.audioState;
    final isRecording = audioState.recordingState == RecordingState.recording;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // 录音按钮
        _buildRecordButton(context, provider, isRecording),
        
        // 播放录音按钮
        _buildPlayRecordingButton(context, provider),
        
        // 录音列表按钮
        _buildRecordingListButton(context, provider),
      ],
    );
  }

  Widget _buildRecordButton(BuildContext context, AppProvider provider, bool isRecording) {
    return ElevatedButton.icon(
      onPressed: isRecording
          ? () => _stopRecording(context, provider)
          : () => _startRecording(context, provider),
      icon: Icon(
        isRecording ? Icons.stop : Icons.mic,
        size: 20,
      ),
      label: Text(isRecording ? '停止录音' : '开始跟读'),
      style: ElevatedButton.styleFrom(
        backgroundColor: isRecording 
            ? Colors.red 
            : const Color(0xFF27AE60),
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }

  Widget _buildPlayRecordingButton(BuildContext context, AppProvider provider) {
    final hasRecordings = provider.currentProgress?.recordingPaths.isNotEmpty ?? false;
    
    return ElevatedButton.icon(
      onPressed: hasRecordings
          ? () => _showRecordingPlayDialog(context, provider)
          : null,
      icon: const Icon(Icons.play_circle_outline, size: 20),
      label: const Text('播放录音'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.9),
        foregroundColor: hasRecordings 
            ? const Color(0xFF4A90E2) 
            : Colors.grey,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }

  Widget _buildRecordingListButton(BuildContext context, AppProvider provider) {
    final recordingCount = provider.currentProgress?.recordingPaths.length ?? 0;
    
    return ElevatedButton.icon(
      onPressed: recordingCount > 0
          ? () => _showRecordingList(context, provider)
          : null,
      icon: const Icon(Icons.list, size: 20),
      label: Text('录音 ($recordingCount)'),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white.withOpacity(0.9),
        foregroundColor: recordingCount > 0 
            ? const Color(0xFF4A90E2) 
            : Colors.grey,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }

  void _showPageSelector(BuildContext context, AppProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('跳转到页面'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('当前页面: ${provider.currentPage} / ${provider.totalPages}'),
            const SizedBox(height: 16),
            Slider(
              value: provider.currentPage.toDouble(),
              min: 1,
              max: provider.totalPages.toDouble(),
              divisions: provider.totalPages - 1,
              label: provider.currentPage.toString(),
              onChanged: (value) {
                provider.updateCurrentPage(value.round());
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Future<void> _startRecording(BuildContext context, AppProvider provider) async {
    final recordingPath = await provider.startRecording();
    if (recordingPath == null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('录音启动失败，请检查麦克风权限'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _stopRecording(BuildContext context, AppProvider provider) async {
    final recordingPath = await provider.stopRecording();
    if (recordingPath != null && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('录音已保存'),
          backgroundColor: Color(0xFF27AE60),
        ),
      );
    }
  }

  void _showRecordingPlayDialog(BuildContext context, AppProvider provider) {
    final recordings = provider.currentProgress?.recordingPaths ?? [];
    if (recordings.isNotEmpty) {
      provider.playRecording(recordings.last);
    }
  }

  void _showRecordingList(BuildContext context, AppProvider provider) {
    final recordings = provider.currentProgress?.recordingPaths ?? [];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('录音列表'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: recordings.length,
            itemBuilder: (context, index) {
              final recording = recordings[index];
              final fileName = recording.split('/').last;
              
              return ListTile(
                leading: const Icon(Icons.audiotrack),
                title: Text('录音 ${index + 1}'),
                subtitle: Text(fileName),
                trailing: IconButton(
                  icon: const Icon(Icons.play_arrow),
                  onPressed: () {
                    provider.playRecording(recording);
                    Navigator.pop(context);
                  },
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
