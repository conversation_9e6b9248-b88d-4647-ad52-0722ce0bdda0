name: reading_app
description: "英语分级阅读App - Cars主题简化版"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.8.1

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  
  # 核心功能
  flutter_pdfview: ^1.3.2
  audioplayers: ^6.0.0
  path_provider: ^2.1.2
  http: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/books/
    - assets/audio/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  remove_alpha_ios: true
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/app_icon.png"
    background_color: "#FF6B35"
    theme_color: "#FF6B35"
  windows:
    generate: true
    image_path: "assets/images/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/app_icon.png"
